#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试Excel表格式的工具
"""

import pandas as pd

def debug_table_format(file_path, sheet_name):
    """调试表格式"""
    print(f"调试表: {sheet_name}")
    print("=" * 80)
    
    try:
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
        
        print(f"总行数: {len(df)}")
        print(f"总列数: {len(df.columns)}")
        print("\n前30行详细内容:")
        print("-" * 80)
        
        for i in range(min(30, len(df))):
            print(f"\n行 {i+1:2d}:")
            row = df.iloc[i]
            for j in range(min(15, len(row))):  # 只显示前15列
                cell = row.iloc[j]
                if pd.notna(cell):
                    cell_str = str(cell).strip()
                    if cell_str:  # 只显示非空内容
                        print(f"  列{j+1:2d}: {cell_str}")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    # 调试几个关键表
    debug_cases = [
        ("cello_dataBaseModelDesign.xls", "账户表"),
        ("xxbz_dataBaseModelDesign.xls", "t_xxbz_user"),
        ("cas_dataBaseModelDesign.xls", "user_info")
    ]
    
    for file_path, sheet_name in debug_cases:
        debug_table_format(file_path, sheet_name)
        print("\n" + "=" * 100 + "\n")

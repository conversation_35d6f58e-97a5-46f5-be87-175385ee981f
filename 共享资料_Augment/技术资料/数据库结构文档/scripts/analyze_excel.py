#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据库设计文档分析工具
用于分析上海联劝公益基金会的数据库结构文档
"""

import pandas as pd
import os
import json
from pathlib import Path

def analyze_excel_file(file_path):
    """分析单个Excel文件的所有工作表"""
    print(f"\n正在分析文件: {file_path}")
    
    try:
        # 读取Excel文件的所有工作表名称
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        print(f"工作表数量: {len(sheet_names)}")
        print(f"工作表名称: {sheet_names}")
        
        file_info = {
            'file_name': os.path.basename(file_path),
            'sheet_count': len(sheet_names),
            'sheet_names': sheet_names,
            'sheets_data': {}
        }
        
        # 分析每个工作表
        for sheet_name in sheet_names:
            print(f"\n  分析工作表: {sheet_name}")
            try:
                df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
                
                # 获取工作表基本信息
                sheet_info = {
                    'rows': len(df),
                    'columns': len(df.columns),
                    'sample_data': []
                }
                
                # 获取前20行数据作为样本
                sample_rows = min(20, len(df))
                for i in range(sample_rows):
                    row_data = []
                    for j in range(len(df.columns)):
                        cell_value = df.iloc[i, j]
                        if pd.isna(cell_value):
                            row_data.append("")
                        else:
                            row_data.append(str(cell_value))
                    sheet_info['sample_data'].append(row_data)
                
                file_info['sheets_data'][sheet_name] = sheet_info
                print(f"    行数: {sheet_info['rows']}, 列数: {sheet_info['columns']}")
                
            except Exception as e:
                print(f"    错误: 无法读取工作表 {sheet_name}: {e}")
                file_info['sheets_data'][sheet_name] = {'error': str(e)}
        
        return file_info
        
    except Exception as e:
        print(f"错误: 无法读取文件 {file_path}: {e}")
        return {'file_name': os.path.basename(file_path), 'error': str(e)}

def main():
    """主函数"""
    current_dir = Path('.')
    excel_files = list(current_dir.glob('*.xls*'))
    
    print(f"找到 {len(excel_files)} 个Excel文件")
    
    all_files_info = {}
    
    for excel_file in excel_files:
        file_info = analyze_excel_file(excel_file)
        all_files_info[file_info['file_name']] = file_info
    
    # 保存分析结果到JSON文件
    with open('excel_analysis_result.json', 'w', encoding='utf-8') as f:
        json.dump(all_files_info, f, ensure_ascii=False, indent=2)
    
    print(f"\n分析完成！结果已保存到 excel_analysis_result.json")
    
    # 生成简要报告
    print("\n=== 文件分析摘要 ===")
    for file_name, info in all_files_info.items():
        if 'error' in info:
            print(f"{file_name}: 错误 - {info['error']}")
        else:
            print(f"{file_name}: {info['sheet_count']} 个工作表")
            for sheet_name in info['sheet_names']:
                sheet_data = info['sheets_data'][sheet_name]
                if 'error' in sheet_data:
                    print(f"  - {sheet_name}: 错误")
                else:
                    print(f"  - {sheet_name}: {sheet_data['rows']}行 x {sheet_data['columns']}列")

if __name__ == "__main__":
    main()

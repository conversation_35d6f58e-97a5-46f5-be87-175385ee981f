#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查具体表结构的工具
"""

import pandas as pd

def check_table_structure(file_path, sheet_name):
    """检查表结构"""
    try:
        print(f"检查表: {sheet_name}")
        print("=" * 60)
        
        # 读取工作表
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
        
        print(f"总行数: {len(df)}")
        print(f"总列数: {len(df.columns)}")
        print("\n详细内容:")
        print("-" * 60)
        
        # 显示所有行的内容
        for i in range(len(df)):
            row_data = []
            for j in range(len(df.columns)):
                cell = df.iloc[i, j]
                if pd.isna(cell):
                    cell_str = ""
                else:
                    cell_str = str(cell).strip()
                
                if cell_str:  # 只显示非空单元格
                    row_data.append(f"列{j+1}: {cell_str}")
            
            if row_data:  # 只显示有内容的行
                print(f"行{i+1:2d}: {' | '.join(row_data)}")
        
        print("\n" + "=" * 60)
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    # 检查几个关键表
    tables_to_check = [
        ("cello_dataBaseModelDesign.xls", "账户表"),
        ("cello_dataBaseModelDesign.xls", "交易流水表"),
        ("xxbz_dataBaseModelDesign.xls", "t_xxbz_user")
    ]
    
    for file_path, sheet_name in tables_to_check:
        check_table_structure(file_path, sheet_name)
        print("\n" + "=" * 80 + "\n")

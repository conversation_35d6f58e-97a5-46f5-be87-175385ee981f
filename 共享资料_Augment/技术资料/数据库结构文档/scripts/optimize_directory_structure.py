#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据字典文档目录结构优化工具
将单文件目录扁平化处理
"""

import os
import shutil
from pathlib import Path

class DirectoryOptimizer:
    def __init__(self, base_dir='数据字典文档'):
        self.base_dir = Path(base_dir)
        self.moved_files = []
        
    def analyze_directory_structure(self):
        """分析当前目录结构"""
        print("📊 分析当前目录结构...")
        
        structure_info = {}
        
        for root, dirs, files in os.walk(self.base_dir):
            root_path = Path(root)
            md_files = [f for f in files if f.endswith('.md')]
            
            if md_files:
                relative_path = root_path.relative_to(self.base_dir)
                structure_info[str(relative_path)] = {
                    'path': root_path,
                    'md_files': md_files,
                    'md_count': len(md_files),
                    'has_subdirs': len(dirs) > 0
                }
        
        return structure_info
    
    def identify_single_file_directories(self, structure_info):
        """识别只包含单个MD文件的目录"""
        single_file_dirs = []
        
        for path_str, info in structure_info.items():
            # 跳过根目录
            if path_str == '.':
                continue
                
            # 只包含一个MD文件且没有子目录的目录
            if info['md_count'] == 1 and not info['has_subdirs']:
                single_file_dirs.append({
                    'path': info['path'],
                    'relative_path': path_str,
                    'file': info['md_files'][0]
                })
        
        return single_file_dirs
    
    def generate_new_filename(self, relative_path, original_filename):
        """生成新的文件名"""
        path_parts = relative_path.split('/')
        
        if len(path_parts) >= 2:
            # 例如: 02-业务模块/用户管理 -> 02-业务模块-用户管理_数据字典.md
            category = path_parts[0]  # 02-业务模块
            subcategory = path_parts[1]  # 用户管理
            
            # 移除原文件名中的重复部分
            base_name = original_filename.replace('.md', '')
            if subcategory in base_name:
                new_filename = f"{category}-{base_name}.md"
            else:
                new_filename = f"{category}-{subcategory}_{base_name}.md"
        else:
            # 单层目录，直接使用目录名作为前缀
            category = path_parts[0]
            base_name = original_filename.replace('.md', '')
            new_filename = f"{category}-{base_name}.md"
        
        return new_filename
    
    def move_files_and_cleanup(self, single_file_dirs):
        """移动文件并清理空目录"""
        print(f"\n🔄 开始优化 {len(single_file_dirs)} 个单文件目录...")
        
        for dir_info in single_file_dirs:
            source_file = dir_info['path'] / dir_info['file']
            new_filename = self.generate_new_filename(dir_info['relative_path'], dir_info['file'])
            target_file = self.base_dir / new_filename
            
            print(f"  移动: {source_file} -> {target_file}")
            
            # 移动文件
            shutil.move(str(source_file), str(target_file))
            
            # 记录移动信息
            self.moved_files.append({
                'original_path': str(source_file),
                'new_path': str(target_file),
                'original_category': dir_info['relative_path'],
                'new_filename': new_filename
            })
            
            # 删除空目录
            try:
                dir_info['path'].rmdir()
                print(f"  删除空目录: {dir_info['path']}")
            except OSError as e:
                print(f"  警告: 无法删除目录 {dir_info['path']}: {e}")
    
    def cleanup_empty_parent_directories(self):
        """清理空的父级目录"""
        print("\n🧹 清理空的父级目录...")
        
        # 检查并删除空的父级目录
        for root, dirs, files in os.walk(self.base_dir, topdown=False):
            root_path = Path(root)
            
            # 跳过根目录
            if root_path == self.base_dir:
                continue
            
            # 如果目录为空，删除它
            try:
                if not any(root_path.iterdir()):
                    root_path.rmdir()
                    print(f"  删除空目录: {root_path}")
            except OSError:
                pass  # 目录不为空或无法删除
    
    def update_readme_structure(self):
        """更新README.md中的文档结构说明"""
        print("\n📝 更新README.md中的文档结构...")
        
        readme_path = self.base_dir / 'README.md'
        
        if not readme_path.exists():
            print("  警告: README.md文件不存在")
            return
        
        # 读取当前README内容
        with open(readme_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 生成新的文档结构
        new_structure = self.generate_new_structure_text()
        
        # 替换文档结构部分
        start_marker = "```\n数据字典文档/"
        end_marker = "```"
        
        start_idx = content.find(start_marker)
        if start_idx != -1:
            end_idx = content.find(end_marker, start_idx + len(start_marker))
            if end_idx != -1:
                # 替换结构说明
                new_content = (content[:start_idx + len(start_marker)] + 
                             new_structure + 
                             content[end_idx:])
                
                # 写回文件
                with open(readme_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print("  ✅ README.md结构说明已更新")
            else:
                print("  警告: 未找到结构说明结束标记")
        else:
            print("  警告: 未找到结构说明开始标记")
    
    def generate_new_structure_text(self):
        """生成新的文档结构文本"""
        structure_lines = []
        
        # 获取当前所有文件
        md_files = list(self.base_dir.glob('*.md'))
        md_files.sort()
        
        # 分类文件
        categories = {
            '01-系统架构概览': [],
            '02-业务模块': [],
            '03-系统模块': [],
            '04-数据关系图谱': [],
            '05-技术规范': [],
            '其他': []
        }
        
        for file_path in md_files:
            filename = file_path.name
            
            # 根据文件名前缀分类
            categorized = False
            for category in categories.keys():
                if filename.startswith(category):
                    categories[category].append(filename)
                    categorized = True
                    break
            
            if not categorized and filename != 'README.md':
                categories['其他'].append(filename)
        
        # 生成结构文本
        structure_lines.append("├── README.md                     # 总体概览和导航")
        
        for category, files in categories.items():
            if files:
                if category == '其他':
                    continue
                    
                # 添加分类注释
                category_desc = {
                    '01-系统架构概览': '# 整体架构和设计说明',
                    '02-业务模块': '# 按业务功能分类的数据字典',
                    '03-系统模块': '# 按系统分类的详细数据字典',
                    '04-数据关系图谱': '# 表间关系和数据流图',
                    '05-技术规范': '# 技术标准和开发规范'
                }
                
                structure_lines.append(f"├── {category_desc.get(category, '')}")
                
                for i, filename in enumerate(files):
                    prefix = "├──" if i < len(files) - 1 else "└──"
                    structure_lines.append(f"│   {prefix} {filename}")
        
        # 添加其他文件
        if categories['其他']:
            for filename in categories['其他']:
                structure_lines.append(f"├── {filename}")
        
        return "\n".join(structure_lines) + "\n"
    
    def optimize(self):
        """执行完整的优化流程"""
        print("🚀 开始优化数据字典文档目录结构...\n")
        
        # 1. 分析当前结构
        structure_info = self.analyze_directory_structure()
        
        # 2. 识别单文件目录
        single_file_dirs = self.identify_single_file_directories(structure_info)
        
        if not single_file_dirs:
            print("✅ 没有需要优化的单文件目录")
            return
        
        print(f"发现 {len(single_file_dirs)} 个单文件目录需要优化:")
        for dir_info in single_file_dirs:
            print(f"  - {dir_info['relative_path']}: {dir_info['file']}")
        
        # 3. 移动文件并清理
        self.move_files_and_cleanup(single_file_dirs)
        
        # 4. 清理空目录
        self.cleanup_empty_parent_directories()
        
        # 5. 更新README
        self.update_readme_structure()
        
        # 6. 显示优化结果
        self.show_optimization_result()
    
    def show_optimization_result(self):
        """显示优化结果"""
        print(f"\n🎉 目录结构优化完成!")
        print(f"📊 优化统计:")
        print(f"  - 移动文件数: {len(self.moved_files)}")
        print(f"  - 删除空目录数: {len(self.moved_files)}")  # 每个移动的文件对应一个删除的目录
        
        print(f"\n📋 文件移动详情:")
        for move_info in self.moved_files:
            print(f"  ✓ {move_info['original_category']} -> {move_info['new_filename']}")

if __name__ == "__main__":
    optimizer = DirectoryOptimizer()
    optimizer.optimize()

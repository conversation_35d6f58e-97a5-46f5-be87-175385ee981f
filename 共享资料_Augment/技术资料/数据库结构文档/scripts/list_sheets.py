#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
列出Excel文件中的所有工作表名称
"""

import pandas as pd
from pathlib import Path

def list_all_sheets():
    """列出所有Excel文件的工作表"""
    current_dir = Path('.')
    excel_files = list(current_dir.glob('*.xls*'))
    
    for excel_file in excel_files:
        print(f"\n文件: {excel_file.name}")
        print("=" * 50)
        
        try:
            excel_obj = pd.ExcelFile(excel_file)
            sheet_names = excel_obj.sheet_names
            
            print(f"工作表数量: {len(sheet_names)}")
            print("工作表列表:")
            
            for i, sheet_name in enumerate(sheet_names[:20], 1):  # 只显示前20个
                print(f"  {i:2d}. {sheet_name}")
            
            if len(sheet_names) > 20:
                print(f"  ... 还有 {len(sheet_names) - 20} 个工作表")
                
        except Exception as e:
            print(f"错误: {e}")

if __name__ == "__main__":
    list_all_sheets()

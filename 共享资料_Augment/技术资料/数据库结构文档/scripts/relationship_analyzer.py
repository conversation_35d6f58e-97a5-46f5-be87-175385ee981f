#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库表间关系分析工具
基于字段名称模式识别外键关系
"""

import json
import re
from collections import defaultdict

class RelationshipAnalyzer:
    def __init__(self):
        # 常见的外键命名模式
        self.fk_patterns = [
            r'(.+)Id$',      # xxxId 格式
            r'(.+)_id$',     # xxx_id 格式
            r'(.+)ID$',      # xxxID 格式
            r'id_(.+)$',     # id_xxx 格式
        ]
        
        # 常见的主键字段名
        self.pk_patterns = ['id', 'ID', 'Id']
        
        # 系统间关联的关键字段
        self.cross_system_fields = [
            'userId', 'user_id', 'entityId', 'entity_id',
            'accountId', 'account_id', 'orderId', 'order_id',
            'projectId', 'project_id', 'activityId', 'activity_id'
        ]
    
    def load_analysis_data(self, file_path='comprehensive_database_analysis.json'):
        """加载分析数据"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def extract_field_info(self, all_systems):
        """提取所有字段信息"""
        all_fields = {}  # {field_name: [(system, table, field_info), ...]}
        all_tables = {}  # {table_name: (system, table_info)}
        
        for system_key, system_info in all_systems.items():
            if 'error' in system_info:
                continue
                
            for table_name, table_info in system_info['tables'].items():
                if not table_info.get('has_structure', False):
                    continue
                
                # 记录表信息
                all_tables[table_name] = (system_key, table_info)
                
                # 记录字段信息
                for field in table_info['fields']:
                    field_name = field['name']
                    if field_name not in all_fields:
                        all_fields[field_name] = []
                    all_fields[field_name].append((system_key, table_name, field))
        
        return all_fields, all_tables
    
    def identify_relationships(self, all_fields, all_tables):
        """识别表间关系"""
        relationships = []
        
        # 1. 基于字段名模式识别外键关系
        for field_name, field_occurrences in all_fields.items():
            # 检查是否符合外键命名模式
            for pattern in self.fk_patterns:
                match = re.match(pattern, field_name)
                if match:
                    referenced_table_name = match.group(1)
                    
                    # 查找可能的被引用表
                    possible_tables = []
                    for table_name in all_tables.keys():
                        if (referenced_table_name.lower() in table_name.lower() or 
                            table_name.lower() in referenced_table_name.lower()):
                            possible_tables.append(table_name)
                    
                    # 记录关系
                    for system, table, field in field_occurrences:
                        for ref_table in possible_tables:
                            if ref_table != table:  # 不是自引用
                                relationships.append({
                                    'type': 'foreign_key',
                                    'from_system': system,
                                    'from_table': table,
                                    'from_field': field_name,
                                    'to_table': ref_table,
                                    'to_field': 'id',  # 假设被引用字段是id
                                    'confidence': 'medium'
                                })
        
        # 2. 识别跨系统关联字段
        cross_system_relations = []
        for field_name in self.cross_system_fields:
            if field_name in all_fields:
                occurrences = all_fields[field_name]
                if len(occurrences) > 1:
                    # 如果同一字段出现在多个系统中，可能是跨系统关联
                    systems_involved = set(occ[0] for occ in occurrences)
                    if len(systems_involved) > 1:
                        cross_system_relations.append({
                            'field_name': field_name,
                            'systems': list(systems_involved),
                            'occurrences': len(occurrences),
                            'tables': [f"{occ[0]}.{occ[1]}" for occ in occurrences]
                        })
        
        return relationships, cross_system_relations
    
    def analyze_business_modules(self, all_systems):
        """分析业务模块"""
        business_modules = {
            '用户管理': {
                'keywords': ['user', 'member', '用户', '会员'],
                'tables': [],
                'systems': set()
            },
            '财务管理': {
                'keywords': ['account', 'transaction', 'payment', 'invoice', '账户', '交易', '支付', '发票'],
                'tables': [],
                'systems': set()
            },
            '捐赠管理': {
                'keywords': ['donation', 'donate', 'fund', '捐赠', '捐款', '基金'],
                'tables': [],
                'systems': set()
            },
            '项目管理': {
                'keywords': ['project', 'activity', '项目', '活动'],
                'tables': [],
                'systems': set()
            },
            '订单管理': {
                'keywords': ['order', '订单'],
                'tables': [],
                'systems': set()
            },
            '证书管理': {
                'keywords': ['certificate', '证书'],
                'tables': [],
                'systems': set()
            },
            '统计分析': {
                'keywords': ['statistics', 'stat', 'report', '统计', '报表'],
                'tables': [],
                'systems': set()
            }
        }
        
        # 根据表名分类到业务模块
        for system_key, system_info in all_systems.items():
            if 'error' in system_info:
                continue
                
            for table_name, table_info in system_info['tables'].items():
                if not table_info.get('has_structure', False):
                    continue
                
                table_name_lower = table_name.lower()
                
                for module_name, module_info in business_modules.items():
                    for keyword in module_info['keywords']:
                        if keyword in table_name_lower:
                            module_info['tables'].append({
                                'system': system_key,
                                'table': table_name,
                                'field_count': table_info['field_count']
                            })
                            module_info['systems'].add(system_key)
                            break
        
        # 转换set为list以便JSON序列化
        for module_info in business_modules.values():
            module_info['systems'] = list(module_info['systems'])
        
        return business_modules
    
    def generate_relationship_report(self):
        """生成关系分析报告"""
        print("正在分析表间关系...")
        
        # 加载数据
        all_systems = self.load_analysis_data()
        all_fields, all_tables = self.extract_field_info(all_systems)
        
        # 分析关系
        relationships, cross_system_relations = self.identify_relationships(all_fields, all_tables)
        business_modules = self.analyze_business_modules(all_systems)
        
        # 生成报告
        report = {
            'summary': {
                'total_fields': len(all_fields),
                'total_tables': len(all_tables),
                'identified_relationships': len(relationships),
                'cross_system_relations': len(cross_system_relations)
            },
            'relationships': relationships,
            'cross_system_relations': cross_system_relations,
            'business_modules': business_modules,
            'common_fields': self.analyze_common_fields(all_fields)
        }
        
        # 保存报告
        with open('relationship_analysis_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印摘要
        self.print_relationship_summary(report)
        
        return report
    
    def analyze_common_fields(self, all_fields):
        """分析常见字段"""
        common_fields = {}
        
        for field_name, occurrences in all_fields.items():
            if len(occurrences) > 3:  # 出现在3个以上表中的字段
                common_fields[field_name] = {
                    'count': len(occurrences),
                    'systems': list(set(occ[0] for occ in occurrences)),
                    'tables': [f"{occ[0]}.{occ[1]}" for occ in occurrences]
                }
        
        return dict(sorted(common_fields.items(), key=lambda x: x[1]['count'], reverse=True))
    
    def print_relationship_summary(self, report):
        """打印关系分析摘要"""
        print("\n" + "=" * 80)
        print("数据库表间关系分析报告")
        print("=" * 80)
        
        summary = report['summary']
        print(f"\n📊 分析摘要:")
        print(f"  • 字段总数: {summary['total_fields']}")
        print(f"  • 表总数: {summary['total_tables']}")
        print(f"  • 识别的关系数: {summary['identified_relationships']}")
        print(f"  • 跨系统关联: {summary['cross_system_relations']}")
        
        print(f"\n🔗 跨系统关联字段:")
        for relation in report['cross_system_relations'][:10]:
            print(f"  • {relation['field_name']}: {relation['occurrences']}次出现，涉及{len(relation['systems'])}个系统")
        
        print(f"\n📋 业务模块分布:")
        for module_name, module_info in report['business_modules'].items():
            if module_info['tables']:
                print(f"  • {module_name}: {len(module_info['tables'])}个表，涉及{len(module_info['systems'])}个系统")
        
        print(f"\n🔄 常见字段 (出现频率最高):")
        for field_name, field_info in list(report['common_fields'].items())[:10]:
            print(f"  • {field_name}: {field_info['count']}次出现，涉及{len(field_info['systems'])}个系统")

if __name__ == "__main__":
    analyzer = RelationshipAnalyzer()
    analyzer.generate_relationship_report()

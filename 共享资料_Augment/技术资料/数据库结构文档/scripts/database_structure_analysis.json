{"cello": {"system_name": "财务支付系统", "file_name": "cello_dataBaseModelDesign", "table_count": 116, "tables": {"项目基本信息": {"table_name": "项目基本信息", "fields": [], "description": "", "indexes": [], "constraints": []}, "目录": {"table_name": "目录", "fields": [], "description": "", "indexes": [], "constraints": []}, "更新履历": {"table_name": "更新履历", "fields": [], "description": "", "indexes": [], "constraints": []}, "更新履历(视图)": {"table_name": "更新履历(视图)", "fields": [], "description": "", "indexes": [], "constraints": []}, "更新履历（存储过程）": {"table_name": "更新履历（存储过程）", "fields": [], "description": "", "indexes": [], "constraints": []}, "存储过程说明": {"table_name": "存储过程说明", "fields": [], "description": "", "indexes": [], "constraints": []}, "配置表": {"table_name": "配置表", "fields": [], "description": "", "indexes": [], "constraints": []}, "账户表": {"table_name": "账户表", "fields": [], "description": "", "indexes": [], "constraints": []}, "帐户类型表": {"table_name": "帐户类型表", "fields": [], "description": "", "indexes": [], "constraints": []}, "帐户状态表": {"table_name": "帐户状态表", "fields": [], "description": "", "indexes": [], "constraints": []}}}, "banyan": {"system_name": "业务管理系统", "file_name": "banyan_dataBaseModelDesign", "table_count": 287, "tables": {"项目基本信息": {"table_name": "项目基本信息", "fields": [], "description": "", "indexes": [], "constraints": []}, "表目录": {"table_name": "表目录", "fields": [], "description": "", "indexes": [], "constraints": []}, "视图目录": {"table_name": "视图目录", "fields": [], "description": "", "indexes": [], "constraints": []}, "函数目录": {"table_name": "函数目录", "fields": [], "description": "", "indexes": [], "constraints": []}, "事件目录": {"table_name": "事件目录", "fields": [], "description": "", "indexes": [], "constraints": []}, "更新履历": {"table_name": "更新履历", "fields": [], "description": "", "indexes": [], "constraints": []}, "更新履历(视图)": {"table_name": "更新履历(视图)", "fields": [], "description": "", "indexes": [], "constraints": []}, "证书模板表2": {"table_name": "证书模板表2", "fields": [], "description": "", "indexes": [], "constraints": []}, "移动文件临时表": {"table_name": "移动文件临时表", "fields": [], "description": "", "indexes": [], "constraints": []}, "管理员帐号表": {"table_name": "管理员帐号表", "fields": [], "description": "", "indexes": [], "constraints": []}}}, "lqscrm": {"system_name": "联劝CRM系统", "file_name": "lqscrm_dataBaseModelDesign", "table_count": 58, "tables": {"项目基本信息": {"table_name": "项目基本信息", "fields": [], "description": "", "indexes": [], "constraints": []}, "目录": {"table_name": "目录", "fields": [], "description": "", "indexes": [], "constraints": []}, "更新履历": {"table_name": "更新履历", "fields": [], "description": "", "indexes": [], "constraints": []}, "更新履历(视图)": {"table_name": "更新履历(视图)", "fields": [], "description": "", "indexes": [], "constraints": []}, "更新履历（存储过程）": {"table_name": "更新履历（存储过程）", "fields": [], "description": "", "indexes": [], "constraints": []}, "微信第三方平台配置表": {"table_name": "微信第三方平台配置表", "fields": [], "description": "", "indexes": [], "constraints": []}, "微信第三方平台调用凭据表": {"table_name": "微信第三方平台调用凭据表", "fields": [], "description": "", "indexes": [], "constraints": []}, "公众号授权信息表": {"table_name": "公众号授权信息表", "fields": [], "description": "", "indexes": [], "constraints": []}, "微信公众账号表": {"table_name": "微信公众账号表", "fields": [], "description": "", "indexes": [], "constraints": []}, "微信公众账号口令表": {"table_name": "微信公众账号口令表", "fields": [], "description": "", "indexes": [], "constraints": []}}}, "egg": {"system_name": "EGG系统", "file_name": "egg_dataBaseModelDesign", "table_count": 78, "tables": {"更新履历": {"table_name": "更新履历", "fields": [], "description": "", "indexes": [], "constraints": []}, "视图更新履历": {"table_name": "视图更新履历", "fields": [], "description": "", "indexes": [], "constraints": []}, "文档列表": {"table_name": "文档列表", "fields": [], "description": "", "indexes": [], "constraints": []}, "视图列表": {"table_name": "视图列表", "fields": [], "description": "", "indexes": [], "constraints": []}, "t_egg_user": {"table_name": "t_egg_user", "fields": [], "description": "", "indexes": [], "constraints": []}, "t_egg_user_team": {"table_name": "t_egg_user_team", "fields": [], "description": "", "indexes": [], "constraints": []}, "t_egg_activity_user": {"table_name": "t_egg_activity_user", "fields": [], "description": "", "indexes": [], "constraints": []}, "t_egg_team": {"table_name": "t_egg_team", "fields": [], "description": "", "indexes": [], "constraints": []}, "t_egg_activity_group_team": {"table_name": "t_egg_activity_group_team", "fields": [], "description": "", "indexes": [], "constraints": []}, "t_egg_activity": {"table_name": "t_egg_activity", "fields": [], "description": "", "indexes": [], "constraints": []}}}, "statistics": {"system_name": "统计分析系统", "file_name": "statistics_dataBaseModelDesign", "table_count": 30, "tables": {"项目基本信息": {"table_name": "项目基本信息", "fields": [], "description": "", "indexes": [], "constraints": []}, "目录": {"table_name": "目录", "fields": [], "description": "", "indexes": [], "constraints": []}, "更新履历": {"table_name": "更新履历", "fields": [], "description": "", "indexes": [], "constraints": []}, "更新履历(视图)": {"table_name": "更新履历(视图)", "fields": [], "description": "", "indexes": [], "constraints": []}, "统计配置表": {"table_name": "统计配置表", "fields": [], "description": "", "indexes": [], "constraints": []}, "注册用户统计表": {"table_name": "注册用户统计表", "fields": [], "description": "", "indexes": [], "constraints": []}, "实体类型表": {"table_name": "实体类型表", "fields": [], "description": "", "indexes": [], "constraints": []}, "支付类型表": {"table_name": "支付类型表", "fields": [], "description": "", "indexes": [], "constraints": []}, "联劝网筹款产品表": {"table_name": "联劝网筹款产品表", "fields": [], "description": "", "indexes": [], "constraints": []}, "每日捐赠统计表": {"table_name": "每日捐赠统计表", "fields": [], "description": "", "indexes": [], "constraints": []}}}, "taxus": {"system_name": "TAXUS系统", "file_name": "taxus_dataBaseModelDesign", "table_count": 143, "tables": {"更新履历": {"table_name": "更新履历", "fields": [], "description": "", "indexes": [], "constraints": []}, "数据库表列表": {"table_name": "数据库表列表", "fields": [], "description": "", "indexes": [], "constraints": []}, "视图存储过程事件": {"table_name": "视图存储过程事件", "fields": [], "description": "", "indexes": [], "constraints": []}, "账户表": {"table_name": "账户表", "fields": [], "description": "", "indexes": [], "constraints": []}, "明细流水表": {"table_name": "明细流水表", "fields": [], "description": "", "indexes": [], "constraints": []}, "帐户状态表": {"table_name": "帐户状态表", "fields": [], "description": "", "indexes": [], "constraints": []}, "帐户类型表": {"table_name": "帐户类型表", "fields": [], "description": "", "indexes": [], "constraints": []}, "平账补充收入记录表": {"table_name": "平账补充收入记录表", "fields": [], "description": "", "indexes": [], "constraints": []}, "行政管理费提取比例表": {"table_name": "行政管理费提取比例表", "fields": [], "description": "", "indexes": [], "constraints": []}, "行政管理费后支付订单的明细流水表": {"table_name": "行政管理费后支付订单的明细流水表", "fields": [], "description": "", "indexes": [], "constraints": []}}}, "crm": {"system_name": "CRM客户关系管理系统", "file_name": "crm_dataBaseModelDesign", "table_count": 148, "tables": {"项目基本信息": {"table_name": "项目基本信息", "fields": [], "description": "", "indexes": [], "constraints": []}, "目录": {"table_name": "目录", "fields": [], "description": "", "indexes": [], "constraints": []}, "更新履历": {"table_name": "更新履历", "fields": [], "description": "", "indexes": [], "constraints": []}, "更新履历(视图)": {"table_name": "更新履历(视图)", "fields": [], "description": "", "indexes": [], "constraints": []}, "更新履历（存储过程）": {"table_name": "更新履历（存储过程）", "fields": [], "description": "", "indexes": [], "constraints": []}, "微信第三方平台配置表": {"table_name": "微信第三方平台配置表", "fields": [], "description": "", "indexes": [], "constraints": []}, "微信第三方平台调用凭据表": {"table_name": "微信第三方平台调用凭据表", "fields": [], "description": "", "indexes": [], "constraints": []}, "公众号授权信息表": {"table_name": "公众号授权信息表", "fields": [], "description": "", "indexes": [], "constraints": []}, "机构微信公众号关联表": {"table_name": "机构微信公众号关联表", "fields": [], "description": "", "indexes": [], "constraints": []}, "微信公众账号表": {"table_name": "微信公众账号表", "fields": [], "description": "", "indexes": [], "constraints": []}}}, "horn": {"system_name": "HORN系统", "file_name": "horn_dataBaseModelDesign", "table_count": 285, "tables": {"项目基本信息": {"table_name": "项目基本信息", "fields": [], "description": "", "indexes": [], "constraints": []}, "目录": {"table_name": "目录", "fields": [], "description": "", "indexes": [], "constraints": []}, "更新履历": {"table_name": "更新履历", "fields": [], "description": "", "indexes": [], "constraints": []}, "更新履历(视图)": {"table_name": "更新履历(视图)", "fields": [], "description": "", "indexes": [], "constraints": []}, "更新履历（存储过程）": {"table_name": "更新履历（存储过程）", "fields": [], "description": "", "indexes": [], "constraints": []}, "登录日志表": {"table_name": "登录日志表", "fields": [], "description": "", "indexes": [], "constraints": []}, "操作日志表": {"table_name": "操作日志表", "fields": [], "description": "", "indexes": [], "constraints": []}, "错误日志表": {"table_name": "错误日志表", "fields": [], "description": "", "indexes": [], "constraints": []}, "基本配置表": {"table_name": "基本配置表", "fields": [], "description": "", "indexes": [], "constraints": []}, "定时器表": {"table_name": "定时器表", "fields": [], "description": "", "indexes": [], "constraints": []}}}, "cas": {"system_name": "CAS认证系统", "file_name": "cas_dataBaseModelDesign", "table_count": 17, "tables": {"更新履历": {"table_name": "更新履历", "fields": [], "description": "", "indexes": [], "constraints": []}, "文档列表": {"table_name": "文档列表", "fields": [], "description": "", "indexes": [], "constraints": []}, "user_info": {"table_name": "user_info", "fields": [], "description": "", "indexes": [], "constraints": []}, "user_merge_info": {"table_name": "user_merge_info", "fields": [], "description": "", "indexes": [], "constraints": []}, "invoice_info": {"table_name": "invoice_info", "fields": [], "description": "", "indexes": [], "constraints": []}, "invoice_title_info": {"table_name": "invoice_title_info", "fields": [], "description": "", "indexes": [], "constraints": []}, "com_config": {"table_name": "com_config", "fields": [], "description": "", "indexes": [], "constraints": []}, "blacklist": {"table_name": "blacklist", "fields": [], "description": "", "indexes": [], "constraints": []}, "validate_mail": {"table_name": "validate_mail", "fields": [], "description": "", "indexes": [], "constraints": []}, "send_message": {"table_name": "send_message", "fields": [], "description": "", "indexes": [], "constraints": []}}}, "xxbz": {"system_name": "小小包子系统", "file_name": "xxbz_dataBaseModelDesign", "table_count": 52, "tables": {"更新履历": {"table_name": "更新履历", "fields": [], "description": "", "indexes": [], "constraints": []}, "视图更新履历": {"table_name": "视图更新履历", "fields": [], "description": "", "indexes": [], "constraints": []}, "文档列表": {"table_name": "文档列表", "fields": [], "description": "", "indexes": [], "constraints": []}, "t_xxbz_activity": {"table_name": "t_xxbz_activity", "fields": [], "description": "", "indexes": [], "constraints": []}, "t_xxbz_user": {"table_name": "t_xxbz_user", "fields": [], "description": "", "indexes": [], "constraints": []}, "t_xxbz_briefMeeting": {"table_name": "t_xxbz_briefMeeting", "fields": [], "description": "", "indexes": [], "constraints": []}, "t_xxbz_activity_user": {"table_name": "t_xxbz_activity_user", "fields": [], "description": "", "indexes": [], "constraints": []}, "t_xxbz_user_family": {"table_name": "t_xxbz_user_family", "fields": [], "description": "", "indexes": [], "constraints": []}, "t_xxbz_family": {"table_name": "t_xxbz_family", "fields": [], "description": "", "indexes": [], "constraints": []}, "t_xxbz_family_team": {"table_name": "t_xxbz_family_team", "fields": [], "description": "", "indexes": [], "constraints": []}}}}
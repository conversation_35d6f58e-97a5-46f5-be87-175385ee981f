#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Excel工作表内容的工具
"""

import pandas as pd
import sys

def inspect_sheet(file_path, sheet_name):
    """检查特定工作表的内容"""
    try:
        print(f"检查文件: {file_path}")
        print(f"工作表: {sheet_name}")
        print("=" * 50)
        
        # 读取工作表
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
        
        print(f"总行数: {len(df)}")
        print(f"总列数: {len(df.columns)}")
        print("\n前20行内容:")
        print("-" * 50)
        
        # 显示前20行
        for i in range(min(20, len(df))):
            row_data = []
            for j in range(min(10, len(df.columns))):  # 只显示前10列
                cell = df.iloc[i, j]
                if pd.isna(cell):
                    row_data.append("")
                else:
                    row_data.append(str(cell)[:20])  # 限制每个单元格显示长度
            
            print(f"行{i+1:2d}: {' | '.join(row_data)}")
        
        print("\n" + "=" * 50)
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    # 检查几个关键的工作表
    test_cases = [
        ("cello_dataBaseModelDesign.xls", "账户表"),
        ("cello_dataBaseModelDesign.xls", "交易流水表"),
        ("crm_dataBaseModelDesign.xls", "user_info"),
        ("banyan_dataBaseModelDesign.xlsx", "Sheet1")
    ]
    
    for file_path, sheet_name in test_cases:
        try:
            inspect_sheet(file_path, sheet_name)
            print("\n" + "=" * 80 + "\n")
        except Exception as e:
            print(f"无法检查 {file_path} - {sheet_name}: {e}")
            print("\n" + "=" * 80 + "\n")

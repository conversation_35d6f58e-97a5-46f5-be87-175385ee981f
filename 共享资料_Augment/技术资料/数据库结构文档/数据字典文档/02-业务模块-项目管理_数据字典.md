# 项目管理模块 数据字典

## 模块概述

**业务模块**: 项目管理  
**关键词**: project, activity, 项目, 活动  
**相关表数量**: 121  
**涉及系统**: 6个  

## 相关表清单

| 序号 | 表名 | 所属系统 | 字段数 | 说明 |
|------|------|----------|--------|------|
| 1 | 公益项目表 | 业务管理系统 | 92 |  |
| 2 | 公益活动表 | HORN系统 | 87 |  |
| 3 | 公益项目表  | HORN系统 | 80 |  |
| 4 | 公益活动表 | 业务管理系统 | 72 |  |
| 5 | 公益活动编辑表 | HORN系统 | 72 |  |
| 6 | 公益项目修改表 | HORN系统 | 66 |  |
| 7 | 项目实行表 | 业务管理系统 | 47 |  |
| 8 | t_egg_activity | EGG系统 | 39 |  |
| 9 | 耐克志愿活动信息表 | HORN系统 | 34 |  |
| 10 | 耐克志愿活动信息修改表  | HORN系统 | 34 |  |
| 11 | 活动报销申请表 | 业务管理系统 | 32 |  |
| 12 | 报名活动配置信息修改表 | HORN系统 | 30 |  |
| 13 | 报名活动配置信息表 | HORN系统 | 30 |  |
| 14 | 志愿者活动表 | HORN系统 | 30 |  |
| 15 | 志愿者活动编辑表 | HORN系统 | 30 |  |
| 16 | t_xxbz_activity | 小小包子系统 | 30 |  |
| 17 | t_egg_activity_group_team | EGG系统 | 28 |  |
| 18 | 活动-团体-队伍表 | HORN系统 | 25 |  |
| 19 | 系列活动项目月捐表 | HORN系统 | 22 |  |
| 20 | 分活动编辑表 | HORN系统 | 22 |  |
| 21 | 分活动表 | HORN系统 | 22 |  |
| 22 | NGO项目表 | 业务管理系统 | 20 |  |
| 23 | 益盒最佳项目表 | HORN系统 | 20 |  |
| 24 | 资助项目临时表 | 业务管理系统 | 19 |  |
| 25 | t_xxbz_activity_enterprise | 小小包子系统 | 19 |  |
| 26 | 项目资料表 | 业务管理系统 | 17 |  |
| 27 | 项目执行反馈表 | 业务管理系统 | 16 |  |
| 28 | 项目执行状况表 | 业务管理系统 | 16 |  |
| 29 | t_egg_activity_teamtype | EGG系统 | 16 |  |
| 30 | 定制专题页项目关联表 | HORN系统 | 16 |  |
| 31 | NGO项目反馈表 | 业务管理系统 | 15 |  |
| 32 | 联劝统筹项目资助退款表 | TAXUS系统 | 15 |  |
| 33 | 外部项目下载图片表 | HORN系统 | 15 |  |
| 34 | 活动-用户表 | HORN系统 | 14 |  |
| 35 | t_xxbz_activityterm | 小小包子系统 | 14 |  |
| 36 | 项目拨付表 | 业务管理系统 | 13 |  |
| 37 | 项目执行预算表 | 业务管理系统 | 13 |  |
| 38 | t_egg_activity_activityterm | EGG系统 | 13 |  |
| 39 | t_egg_fund_project | EGG系统 | 13 |  |
| 40 | 耐克在线项目推荐表 | HORN系统 | 13 |  |
| 41 | 项目表 | 业务管理系统 | 12 |  |
| 42 | NGO项目预算表 | 业务管理系统 | 12 |  |
| 43 | 活动项目月捐统计表 | 财务支付系统 | 11 |  |
| 44 | 活动报销清单表 | 业务管理系统 | 11 |  |
| 45 | 项目审核表 | 业务管理系统 | 11 |  |
| 46 | 项目经验表 | 业务管理系统 | 11 |  |
| 47 | 项目计划表 | 业务管理系统 | 11 |  |
| 48 | 项目执行反馈文件表 | 业务管理系统 | 11 |  |
| 49 | NGO项目其他费用支出表 | 业务管理系统 | 11 |  |
| 50 | 义卖活动电子劵记录表 | HORN系统 | 11 |  |
| 51 | 联劝官网项目案例表 | 业务管理系统 | 10 |  |
| 52 | 项目申报平台表 | 业务管理系统 | 10 |  |
| 53 | 线下项目表  | 业务管理系统 | 10 |  |
| 54 | 项目执行表 | 业务管理系统 | 10 |  |
| 55 | 同类项目表 | 业务管理系统 | 10 |  |
| 56 | 活动项目商品关系表 | HORN系统 | 10 |  |
| 57 | 活动项目商品关系编辑表 | HORN系统 | 10 |  |
| 58 | 专题页项目配捐比例调整表 | HORN系统 | 10 |  |
| 59 | 项目捐赠耐克配捐记录表 | 财务支付系统 | 9 |  |
| 60 | 项目申请计划书表 | 业务管理系统 | 9 |  |
| 61 | 项目管理费用支出表 | 业务管理系统 | 9 |  |
| 62 | 资助项目反馈审核信息表 | 业务管理系统 | 9 |  |
| 63 | 公益活动表-公众参与 | 业务管理系统 | 9 |  |
| 64 | 活动邀请码表 | HORN系统 | 9 |  |
| 65 | 外部项目图片状态表 | HORN系统 | 9 |  |
| 66 | 项目捐赠配捐统计表 | HORN系统 | 9 |  |
| 67 | 益盒最佳项目选择表 | HORN系统 | 9 |  |
| 68 | 项目执行团队成员表 | 业务管理系统 | 8 |  |
| 69 | 项目目标表 | 业务管理系统 | 8 |  |
| 70 | NGO项目管理费用支出表 | 业务管理系统 | 8 |  |
| 71 | t_egg_activity_enterprise | EGG系统 | 8 |  |
| 72 | 电子劵义卖活动配置表 | HORN系统 | 8 |  |
| 73 | 定制专题页项目类型表 | HORN系统 | 8 |  |
| 74 | 耐克在线推荐项目邮件发送记录 | HORN系统 | 8 |  |
| 75 | 用户收藏的项目表 | HORN系统 | 8 |  |
| 76 | t_egg_activity_user | EGG系统 | 7 |  |
| 77 | 项目实施地点表 | 业务管理系统 | 6 |  |
| 78 | NGO项目计划表 | 业务管理系统 | 6 |  |
| 79 | NGO项目拨付记录表 | 业务管理系统 | 6 |  |
| 80 | t_egg_project | EGG系统 | 6 |  |
| 81 | 子项目表 | TAXUS系统 | 6 |  |
| 82 | 公益项目外部情况表 | HORN系统 | 6 |  |
| 83 | 项目执行省份表 | 业务管理系统 | 5 |  |
| 84 | 项目筹款信息表 | 业务管理系统 | 5 |  |
| 85 | 项目合作机构表 | 业务管理系统 | 5 |  |
| 86 | 资助项目临时地域表 | 业务管理系统 | 5 |  |
| 87 | t_egg_activity_mileage | EGG系统 | 5 |  |
| 88 | 特殊扣费项目表 | TAXUS系统 | 5 |  |
| 89 | 项目预算表 | HORN系统 | 5 |  |
| 90 | 项目预算修改表 | HORN系统 | 5 |  |
| 91 | 活动预算表 | HORN系统 | 5 |  |
| 92 | 活动预算修改表 | HORN系统 | 5 |  |
| 93 | 项目执行图片表 | 业务管理系统 | 4 |  |
| 94 | 项目产出表 | 业务管理系统 | 4 |  |
| 95 | 项目发布平台表 | 业务管理系统 | 4 |  |
| 96 | t_egg_activityterm | EGG系统 | 4 |  |
| 97 | t_egg_activityterm_apply | EGG系统 | 4 |  |
| 98 | 机构项目外部筹款订单最后导入时间记录表 | HORN系统 | 4 |  |
| 99 | 项目推送表 | HORN系统 | 4 |  |
| 100 | 义买活动电子劵记录明细表 | HORN系统 | 4 |  |
| 101 | t_xxbz_activity_user | 小小包子系统 | 4 |  |
| 102 | t_xxbz_activityterm_apply | 小小包子系统 | 4 |  |
| 103 | 公益项目子母计划关系表 | HORN系统 | 3 |  |
| 104 | 活动商品关系表 | 业务管理系统 | 0 |  |
| 105 | 活动项目标签表 | 业务管理系统 | 0 |  |
| 106 | 公益活动详情表 | 业务管理系统 | 0 |  |
| 107 | 活动队伍关联表 | 业务管理系统 | 0 |  |
| 108 | 首页轮播活动设置表 | 业务管理系统 | 0 |  |
| 109 | 企业项目表 | 业务管理系统 | 0 |  |
| 110 | 项目企业认领表 | 业务管理系统 | 0 |  |
| 111 | 公益项目筹款预算表 | 业务管理系统 | 0 |  |
| 112 | 公益项目详情表 | 业务管理系统 | 0 |  |
| 113 | 公益项目进展表 | 业务管理系统 | 0 |  |
| 114 | 公益项目进展详情表 | 业务管理系统 | 0 |  |
| 115 | 项目统计表 | 业务管理系统 | 0 |  |
| 116 | 项目推荐设置表 | 业务管理系统 | 0 |  |
| 117 | 项目分值表 | 业务管理系统 | 0 |  |
| 118 | 项目筹款标的表 | 业务管理系统 | 0 |  |
| 119 | 公益系列活动项目表 | 业务管理系统 | 0 |  |
| 120 | 项目领域关联表 | HORN系统 | 0 |  |
| 121 | 项目领域关联修改表 | HORN系统 | 0 |  |

## 主要表结构详情

### 公益项目表 (业务管理系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | projectId | varchar | 32 |  |  | 项目ID(PDT开头） |
| 3 | projectName | varchar | 64 | 否 |  | 项目名称 |
| 4 | projectFields | varchar | 32 |  |  | 项目领域（只要一级领域） |
| 5 | cycle | int | 1 |  |  | 项目周期（1:1个月   2:2个月   3:3个月  6:6个月   12:12个月   99：无期限） |
| 6 | fundraisingMaxAmount | decimal | 15,2 |  |  | 筹款目标（根据项目筹款结束方式设置，可以为空） |
| 7 | projectFundEnd | datetime |  |  |  | 项目筹款结束时间（根据项目筹款结束方式设置，可以为空） |
| 8 | reason | text |  |  |  | 发起缘由 |
| 9 | projectBigPicId | int | 11 |  |  | 项目主题大图（图片ID，详情页面显示） |
| 10 | projectSmallPicId | int | 11 |  |  | 项目主题小图（图片ID，首页和列表页面显示） |
| 11 | projectIntroduction | varchar | 100 |  |  | 项目简介 |
| 12 | projectTarget1 | decimal | 15,2 |  | 20 | 项目标的1 |
| 13 | projectTarget2 | decimal | 15,2 |  | 50 | 项目标的2 |
| 14 | projectTarget3 | decimal | 15,2 |  | 100 | 项目标的3 |
| 15 | keywords1 | int | 11 |  |  | 关键字1 |
| 16 | keywords2 | int | 11 |  |  | 关键字2 |
| 17 | keywords3 | int | 11 |  |  | 关键字3 |
| 18 | donatedGpId | varchar | 32 |  |  | 受捐群体名称 |
| 19 | donatedGpName | varchar | 20 |  |  | 受捐群体负责人姓名 |
| 20 | donatedGpTel | varchar | 20 |  |  | 受捐群体联系电话 |
| 21 | donatedGpAddress | varchar | 100 |  |  | 受捐群体地址 |
| 22 | status | int | 1 | 否 | 0 | 项目状态（0：草稿；1：审核中；2：进行中（募集中或募集结束）；3：审核不通过；4：已关闭；5:第三方平台） |
| 23 | institutionId | int | 11 | 否 |  | 机构ID |
| 24 | holderId | int | 11 |  |  | 所属ID（填写所属活动ID或所属基金ID、所属项目ID） |
| 25 | type | int | 1 | 否 | 0 | 所属类型（1：独立项目；2：基金；3：合作 ；5：公募基金…） |
| 26 | detailPic1 | int | 11 |  |  | 详情图片1（图片ID） |
| 27 | detailTitle1 | varchar | 50 |  |  | 详情标题1 |
| 28 | detailInfo1 | varchar | 100 |  |  | 详情描述1 |
| 29 | detailPic2 | int | 11 |  |  | 详情图片2（图片ID） |
| 30 | detailTitle2 | varchar | 50 |  |  | 详情标题2 |
| 31 | detailInfo2 | varchar | 100 |  |  | 详情描述2 |
| 32 | detailPic3 | int | 11 |  |  | 详情图片3（图片ID） |
| 33 | detailTitle3 | varchar | 50 |  |  | 详情标题3 |
| 34 | detailInfo3 | varchar | 100 |  |  | 详情描述3 |
| 35 | detailPic4 | int | 11 |  |  | 详情图片4（图片ID） |
| 36 | detailTitle4 | varchar | 50 |  |  | 详情标题4 |
| 37 | detailInfo4 | varchar | 100 |  |  | 详情描述4 |
| 38 | detailPic5 | int | 11 |  |  | 详情图片5（图片ID） |
| 39 | detailTitle5 | varchar | 50 |  |  | 详情标题5 |
| 40 | detailInfo5 | varchar | 100 |  |  | 详情描述5 |
| 41 | detailPic6 | int | 11 |  |  | 详情图片6（图片ID） |
| 42 | detailTitle6 | varchar | 50 |  |  | 详情标题1 |
| 43 | detailInfo6 | varchar | 100 |  |  | 详情描述6 |
| 44 | detailPic7 | int | 11 |  |  | 详情图片7（图片ID） |
| 45 | detailTitle7 | varchar | 50 |  |  | 详情标题7 |
| 46 | detailInfo7 | varchar | 100 |  |  | 详情描述7 |
| 47 | detailPic8 | int | 11 |  |  | 详情图片8（图片ID） |
| 48 | detailTitle8 | varchar | 50 |  |  | 详情标题8 |
| 49 | detailInfo8 | varchar | 100 |  |  | 详情描述8 |
| 50 | detailPic9 | int | 11 |  |  | 详情图片9（图片ID） |
| 51 | detailTitle9 | varchar | 50 |  |  | 详情标题9 |
| 52 | detailInfo9 | varchar | 100 |  |  | 详情描述9 |
| 53 | detailPic10 | int | 11 |  |  | 详情图片10（图片ID） |
| 54 | detailTitle10 | varchar | 50 |  |  | 详情标题10 |
| 55 | detailInfo10 | varchar | 100 |  |  | 详情描述10 |
| 56 | amount | decimal | 15,2 |  |  | 预算总额 |
| 57 | reviewDetail | varchar | 200 |  |  | 审核不通过的理由 |
| 58 | reviewTime | datetime |  |  |  | 审核时间 |
| 59 | reviewer | varchar | 32 |  |  | 审核者（用户名） |
| 60 | reviewNickName | varchar | 32 |  |  | 审核者昵称 |
| 61 | applyTime | datetime |  |  |  | 申请时间 |
| 62 | insertTime | datetime |  | 否 |  | 创建时间 |
| 63 | closeTime | datetime |  |  |  | 结束筹款时间 |
| 64 | sortNo | int | 11 |  |  | 排序（联劝网上的显示规则） |
| 65 | fundraisingAccount | varchar | 32 |  |  | 筹款账户 |
| 66 | closeReason | varchar | 200 |  |  | 结束筹款理由 |
| 67 | closer | varchar | 32 |  |  | 结束者（机构名称或管理员用户名） |
| 68 | snakerOrderId | varchar | 32 |  |  | 流程实例ID |
| 69 | snakerStepName | varchar | 100 |  |  | 当前任务名 |
| 70 | snakerStatus | varchar | 100 |  |  | 当前状态 |
| 71 | anonymousAccount | varchar | 100 |  |  | 匿名账户 |
| 72 | adminFeeType | tinyint | 1 |  |  | 行政管理费提取类型（0：不提取 1：固定比例提取 2：累进金额按约定比例提取 ） |
| 73 | adminFeeFixedRate | decimal | 5,2 |  |  | 行政管理费固定提取比例(非固定比例为空) |
| 74 | publishPlatformId | int | 11 |  |  | 项目发布平台ID(为空时，联劝网线上发布) |
| 75 | pointTo | int | 1 | 否 |  | 用款指向（1：专款专用；2：无指向性） |
| 76 | ifFullStop | int | 1 | 否 | 0 | 筹满截止标志（0：不截止；1：截止） |
| 77 | webSite | varchar | 255 |  |  | 筹款网址 |
| 78 | firstProjectName | varchar | 64 |  |  | 第一次录入的项目名称 |
| 79 | showListFlag | int | 1 | 否 | 0 | 联劝网列表是否展示（0：不展示；1：展示） |
| 80 | showIndexFlag | int | 1 | 否 | 0 | 联劝网首页是否展示（0：不展示；1：展示） |
| 81 | cycleEndTime | datetime |  |  |  | 项目最终结束时间/周期用户反馈邮件任务生成时间 |
| 82 | endFeedbackMark | int | 1 | 否 | 0 | 项目最终结束用户反馈标志（0：未生成反馈邮件；1：已生成反馈邮件；2：历史数据不用反馈） |
| 83 | mark | int | 1 | 否 | 0 | 筹款是否已结束的标志位（0：未筹款；1：筹款中；2：筹款结束；） |
| 84 | markOfEndTime | datetime |  |  |  | 筹款结束标志位更新时间（mark值更新为2时的时间） |
| 85 | ifNeedPhoneAM | int | 1 | 否 | 0 | 匿名捐赠是否必填手机（0：非必填；1：必填） |
| 86 | ifNeedEmailAM | int | 1 | 否 | 0 | 匿名捐赠是否必填邮箱（0：非必填；1：必填） |
| 87 | coverage | varchar | 32 |  |  | 项目所在地域（地域ID的组合，用,连接。业务侧新增加的项目不允许为空） |
| 88 | ifSupportEpClaim | int | 1 | 否 | 0 | 是否支持企业认领（0：不支持；1：支持） |
| 89 | previewCode | varchar | 32 |  |  | 预览校验码 |
| 90 | recordNumber | varchar | 255 |  |  | 备案编号 |
| 91 | historyDB | int | 4 |  | 0 | 第三方平台的筹款产品，订单数据保存的数据库。0表示正常，未迁移 |
| 92 | flg | int | 1 |  | 0 | 新增第三方产品时用（3：联劝网项目；4：联劝网月捐项目；5：非联劝网项目） |

### 公益活动表 (HORN系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | activityId | varchar | 32 |  |  | 活动id（act开头) |
| 3 | activityName | varchar | 20 |  |  | 活动名称 |
| 4 | subjectPicId | int | 11 |  |  | 主题图片ID，列表页面（主题图片比例为8：9） |
| 5 | subjectBigPicId | int | 11 |  |  | 主题图片ID，详情页面（主题图片比例为4:3） |
| 6 | activityIntroduction | text |  |  |  | 活动简介 |
| 7 | startTime | datetime |  |  |  | 活动开始时间 |
| 8 | endTime | datetime |  |  |  | 活动结束时间 |
| 9 | status | int | 1 |  |  | 活动状态（0：未申请；1：在线申请中；2：在线上（审批通过）；3：中途取消；4：线下活动；5：审核不通过；6：已结束；7：测试发布；8：已删除；9：待认领；10：已认领；99：配置中；98：已配置） |
| 10 | fundraisingAccount | varchar | 32 |  |  | 筹款账户 |
| 11 | fundraisingStartTime | datetime |  |  |  | 筹款开始时间 |
| 12 | fundraisingEndTime | datetime |  |  |  | 筹款截止时间 |
| 13 | anonymousAccount | varchar | 32 |  |  | 匿名捐赠账户 |
| 14 | institutionId | int | 11 |  |  | 机构ID |
| 15 | reviewDetail | text |  |  |  | 审核不通过的理由 |
| 16 | reviewTime | datetime |  |  |  | 审核时间 |
| 17 | reviewer | varchar | 32 |  |  | 审核者（用户名） |
| 18 | reviewNickName | varchar | 32 |  |  | 审核者昵称 |
| 19 | applyTime | datetime |  |  |  | 申请时间 |
| 20 | isHaveWebSite | int | 1 |  |  | 是否有独立网站（0：有独立网站；1：没有独立网站） |
| 21 | webSiteUrl | varchar | 500 |  |  | 独立网站访问地址（PC版） |
| 22 | webSiteUrlM | varchar | 200 |  |  | 独立网站访问地址（PAD版、手机版） |
| 23 | showTime | datetime |  |  |  | 活动线上展示时间 |
| 24 | useAds | int | 1 |  |  | 报名类型（ 0：组队报名；1：不需要报名；2：个人报名；99：义卖活动） |
| 25 | createTime | datetime |  |  |  | 活动创建时间 |
| 26 | registrationAccount | varchar | 32 |  |  | 报名费账号（要在线报名并需要支付报名费时为必须项，审批通过时开通） |
| 27 | drawAccount | varchar | 32 |  |  | 抽签费账户 |
| 28 | version | varchar | 10 |  |  | 爱扑满版本号 |
| 29 | businessid | varchar | 32 |  |  | 感谢卡模板id |
| 30 | closer | varchar | 32 |  |  | 取消者（机构名称或管理员用户名） |
| 31 | closeReanson | varchar | 200 |  |  | 取消理由 |
| 32 | closeTime | datetime |  |  |  | 取消时间 |
| 33 | target1 | decimal | 15,2 |  |  | 筹款标的1 |
| 34 | target2 | decimal | 15,2 |  |  | 筹款标的2 |
| 35 | target3 | decimal | 15,2 |  |  | 筹款标的3 |
| 36 | indexShowOrder | int | 11 |  |  | 首页显示顺 |
| 37 | holderId | varvhar | 11 |  |  | 所属ID（认领公募基金会GUID） |
| 38 | claimStatus | int | 1 |  |  | 认领状态（0：未认领；1：已认领；2：自己发起（公募基金专用）） |
| 39 | contacts | varchar | 100 |  |  | 主办方联系方式/客服电话 |
| 40 | ifNeedFund | int | 1 |  |  | 是否需要筹款（0：要筹款；1：不要筹款） |
| 41 | showVideoFlag | int | 1 |  |  | 是否显示视频（0：不显示；1：显示） |
| 42 | videoId | String | 32 |  |  | 视频vid |
| 43 | videoSize | String | 32 |  |  | 视频大小 （含单位） |
| 44 | supportEn | int | 1 |  |  | 是否支持英文版（0：不支持；1：支持） |
| 45 | recordNumber | varchar | 255 |  |  | 备案编号 |
| 46 | logisticalAccount | varchar | 32 |  |  | 快递费账户（义买活动时存在） |
| 47 | lastEditStep | int | 2,0 |  |  | 上次关闭前最后修改的步骤 |
| 48 | fundraiseEntityGuid | varchar | 255 |  |  | 支付信息实体id |
| 49 | currentAccountType | int | 1 |  | 0 | 当前账户类型（0：未开；1：测试；2：正式） |
| 50 | isCollect | int | 1 |  | 0 | 是否收集用户信息 0不收集；1:收集； |
| 51 | lianQuanShowType | int | 1 |  |  | 在联劝网上的显示方式（0：慈善募捐；1：个人求助） |
| 52 | applyConfigTime | datetime |  |  |  | 申请配置的时间 |
| 53 | ifNeedActivityFund | int | 1 |  |  | 是否需要直捐给活动（0：需要；1：不需要；2：需要且初期为直捐活动） |
| 54 | details | varchar | 255 |  |  |  |
| 55 | venue | varchar | 255 |  |  |  |
| 56 | isMatch | int | 1 |  | 0 | 是否为需要配捐的活动（0：否；1：是） |
| 57 | ifNeedInsurance | int | 1 |  | 0 | 是否购买保险，0不购买，1购买 |
| 58 | updateTime | datetime |  |  |  | 更新时间 |
| 59 | numStartTime | date | 0 |  |  | 备案编号开始时间 |
| 60 | numEndTime | date | 0 |  |  | 备案编号结束时间 |
| 61 | numDetailUrl | date | 0 |  |  | 备案编号详情网址 |
| 62 | targetStr1 | varchar | 15 |  |  | 筹款标的1（别名） |
| 63 | targetStr2 | varchar | 15 |  |  | 筹款标的2（别名） |
| 64 | targetStr3 | varchar | 15 |  |  | 筹款标的3（别名） |
| 65 | targetflg | int | 1 |  | 0 | 标的别名标志（0：不使用；1：使用） |
| 66 | lastProgressTime | datetime | 0 |  |  | 最新的反馈提交日期 |
| 67 | remindProgressTime | datetime | 0 |  |  | 反馈提醒周期/结项逾期期限的开始时间 |
| 68 | isOss | int | 1 | 否 | 0 | 全局OSS标记 （0:未OSS化，1:已全部oss化） |
| 69 | isNeedCom | int | 1 | 否 | 0 | 是否需要商品义卖（0：否；1：是） |
| 70 | isRisk | int | 1 |  | 0 | 是否风控下线（0：否；1：是） |
| 71 | isOss1 | int | 1 | 否 | 0 | 小图OSS标记（0:未OSS化，1:已oss化） |
| 72 | isOss2 | int | 1 | 否 | 0 | 大图OSS标记（0:未OSS化，1:已oss化） |
| 73 | ossUrl1 | varchar | 255 |  |  | 小图OSS化的路径 |
| 74 | ossUrl2 | varchar | 255 |  |  | 大图OSS化的路径 |
| 75 | delflg1 | int | 1 | 否 | 0 | 小图OSS化删除标记（0:不需要处理，1:需要去oss端删除） |
| 76 | delflg2 | int | 1 | 否 | 0 | 大图OSS化删除标记（0:不需要处理，1:需要去oss端删除） |
| 77 | toolsOperateFlg | int | 1 | 否 | 0 | 爱扑满审核标志（0：不审核；1：审核） |
| 78 | fundraisingMoney | decimal | 15,2 | 是 | 0 | 资金同步筹款金额 |
| 79 | fundraisingCount | int | 11 | 是 | 0 | 资金同步筹款笔数 |
| 80 | fundMatch | decimal | 15,2 | 是 | 0 | 企业配捐金额 |
| 81 | countMatch | int | 11 | 是 | 0 | 企业配捐笔数 |
| 82 | amount | decimal | 15,2 | 是 | 0 | 预算总金额 |
| 83 | endProgressFlag | int | 1 | 是 | 0 | 是否有结项反馈（0：无 1：有） |
| 84 | portraitFileId | int |  |  |  | 未成年人肖像协议文件 |
| 85 | publicReview | int | 0 |  |  | 公募审核状态（0：公募无需审核、不审核；1：公募审核不通过；2：公募审核中；3：公募审核通过；）——已认领未上线的活动 |
| 86 | coverage | varchar | 1024 |  |  | 活动所在地域（地域ID的组合，用,连接。业务侧新增加的活动不允许为空） |
| 87 | donatePcFlg | int | 1 | 否 | 0 | 电脑端捐赠支持标志（0：不支持；1：支持） |

### 公益项目表  (HORN系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | projectId | varchar | 32 |  |  | 项目ID(PDT开头） |
| 3 | projectName | varchar | 64 |  |  | 项目名称 |
| 4 | cycle | int | 1 |  |  | 项目周期（1:1个月   2:2个月   3:3个月  6:6个月   12:12个月   99：无期限） |
| 5 | fundraisingMaxAmount | decimal | 15,2 |  |  | 筹款目标（根据项目筹款结束方式设置，可以为空） |
| 6 | projectFundStart | datetime |  |  |  | 项目筹款开始时间 |
| 7 | projectFundEnd | datetime |  |  |  | 项目筹款结束时间（根据项目筹款结束方式设置，可以为空） |
| 8 | reason | text |  |  |  | 发起缘由 |
| 9 | projectBigPicId | int | 11 |  |  | 项目主题大图（图片ID，详情页面显示） |
| 10 | projectSmallPicId | int | 11 |  |  | 项目主题小图（图片ID，首页和列表页面显示） |
| 11 | projectIntroduction | varchar | 100 |  |  | 项目简介 |
| 12 | projectTarget1 | decimal | 15,2 |  |  | 项目标的1 |
| 13 | projectTarget2 | decimal | 15,2 |  |  | 项目标的2 |
| 14 | projectTarget3 | decimal | 15,2 |  |  | 项目标的3 |
| 15 | status | int | 1 |  |  | 项目状态（0：草稿；1：审核中；2：进行中；3：审核不通过；4：已关闭（中途取消）；5:第三方平台；6：已结束；8：已删除；9：待认领；10：已认领；） |
| 16 | institutionId | int | 11 |  |  | 机构ID |
| 17 | holderId | int | 11 |  |  | 所属ID（填写所属活动ID或所属基金ID、所属项目ID） |
| 18 | type | int | 1 |  |  | 所属类型（0：联劝网项目；1：外部接口项目；3：基金（2018年5月平台分离前历史数据）4：母计划项目；5：子计划项目） |
| 19 | claimStatus | int | 1 |  |  | 认领状态（0：未认领；1：已认领；2：自己发起（公募基金专用）） |
| 20 | amount | decimal | 15,2 |  |  | 预算总额 |
| 21 | reviewDetail | varchar | 200 |  |  | 审核不通过的理由 |
| 22 | reviewTime | datetime |  |  |  | 审核时间 |
| 23 | reviewer | varchar | 32 |  |  | 审核者（用户名） |
| 24 | reviewNickName | varchar | 32 |  |  | 审核者昵称 |
| 25 | applyTime | datetime |  |  |  | 申请时间 |
| 26 | insertTime | datetime |  |  |  | 创建时间 |
| 27 | closeTime | datetime |  |  |  | 结束筹款时间 |
| 28 | sortNo | int | 11 |  |  | 排序（联劝网上的显示规则） |
| 29 | fundraisingAccount | varchar | 32 |  |  | 筹款账户 |
| 30 | closeReason | varchar | 200 |  |  | 结束筹款理由 |
| 31 | closer | varchar | 32 |  |  | 结束者（机构名称或管理员用户名） |
| 32 | anonymousAccount | varchar | 100 |  |  | 匿名账户 |
| 33 | ifFullStop | int | 1 |  |  | 筹满截止标志（0：不截止；1：截止） |
| 34 | webSite | varchar | 255 |  |  | 筹款网址 |
| 35 | showListFlag | int | 1 |  |  | 联劝网列表是否展示（0：不展示；1：展示） |
| 36 | showIndexFlag | int | 1 |  |  | 联劝网首页是否展示（0：不展示；1：展示） |
| 37 | mark | int | 1 |  |  | 筹款是否已结束的标志位（0：未筹款；1：筹款中；2：筹款结束；） |
| 38 | markOfEndTime | datetime |  |  |  | 筹款结束标志位更新时间（mark值更新为2时的时间） |
| 39 | coverage | varchar | 255 |  |  | 项目所在地域（地域ID的组合，用,连接。业务侧新增加的项目不允许为空） |
| 40 | ifSupportEpClaim | int | 1 |  |  | 是否支持企业认领（0：不支持；1：支持） |
| 41 | recordNumber | varchar | 255 |  |  | 备案编号 |
| 42 | createStep | int | 1 |  |  | 创建步数（1：编辑项目；2：上传图片；3：编辑详情；4：编制预算；5：收集信息；6：提交审核） |
| 43 | currentAccountType | int | 1 |  | 0 | 当前账户类型（0：未开；1：测试；2：正式） |
| 44 | fundraiseEntityGuid | varchar | 255 |  |  | 付款信息guid |
| 45 | isCollect | int | 1 |  |  | 是否收集用户信息 1:收集；2不收集 |
| 46 | lianQuanShowType | int | 1 |  |  | 在联劝网上的显示方式（0：慈善募捐；1：个人求助） |
| 47 | showIndex | int | 11 |  |  | 优先排序标记 |
| 48 | callbackurl | varchar | 255 |  |  | 回调URL，用于项目上线后通知 |
| 49 | updateTime | datetime |  |  |  | 更新时间 |
| 50 | isMatch | int | 1 |  | 0 | 是否有配捐（0：否；1：是） |
| 51 | numStartTime | date | 0 |  |  | 备案编号开始时间 |
| 52 | numEndTime | date | 0 |  |  | 备案编号结束时间 |
| 53 | numDetailUrl | date | 0 |  |  | 备案编号详情网址 |
| 54 | targetStr1 | varchar | 15 |  |  | 筹款标的1（别名） |
| 55 | targetStr1 | varchar | 15 |  |  | 筹款标的2（别名） |
| 56 | targetStr1 | varchar | 15 |  |  | 筹款标的2（别名） |
| 57 | targetflg | int | 1 |  | 0 | 标的别名标志（0：不使用；1：使用） |
| 58 | lastProgressTime | datetime | 0 |  |  | 最新的反馈提交日期 |
| 59 | remindProgressTime | datetime | 0 |  |  | 反馈提醒周期/结项逾期期限的开始时间 |
| 60 | isOss | int | 1 | 否 | 0 | 全局OSS标记 （0:未OSS化，1:已全部oss化） |
| 61 | logisticalAccount | varchar | 32 |  |  | 快递费账户（需要商品义买时存在） |
| 62 | isNeedCom | int | 1 | 否 | 0 | 是否需要商品义卖（0：否；1：是） |
| 63 | isRisk | int | 1 |  | 0 | 是否风控下线（0：否；1：是） |
| 64 | isOss1 | int | 1 | 否 | 0 | 小图OSS标记（0:未OSS化，1:已oss化） |
| 65 | isOss2 | int | 1 | 否 | 0 | 大图OSS标记（0:未OSS化，1:已oss化） |
| 66 | ossUrl1 | varchar | 255 |  |  | 小图OSS化的路径 |
| 67 | ossUrl2 | varchar | 255 |  |  | 大图OSS化的路径 |
| 68 | delflg1 | int | 1 | 否 | 0 | 小图OSS化删除标记（0:不需要处理，1:需要去oss端删除） |
| 69 | delflg2 | int | 1 | 否 | 0 | 大图OSS化删除标记（0:不需要处理，1:需要去oss端删除） |
| 70 | toolsOperateFlg | int | 1 | 否 | 0 | 爱扑满审核标志（0：不审核；1：审核） |
| 71 | fundraisingMoney | decimal | 15,2 | 是 | 0 | 资金同步筹款金额 |
| 72 | fundraisingCount | int | 11 | 是 | 0 | 资金同步筹款笔数 |
| 73 | fundMatch | decimal | 15,2 | 是 | 0 | 企业配捐金额 |
| 74 | countMatch | int | 11 | 是 | 0 | 企业配捐笔数 |
| 75 | stopAccountFlag | int | 1 |  | 0 | 筹满截止操作记录（0：未操作；1：已操作，账户停止筹款；2：账户已修改为其他状态） |
| 76 | endProgressFlag | int | 1 | 是 | 0 | 是否有结项反馈（0：无 1：有） |
| 77 | portraitFileId | int |  |  |  | 未成年人肖像协议文件 |
| 78 | publicReview | int | 0 |  |  | 公募审核状态（0：公募无需审核、不审核；1：公募审核不通过；2：公募审核中；3：公募审核通过；）——已认领未上线的项目 |
| 79 | subProReviewIns | String | 32 | 是 |  | 子计划审核机构（母计划的创建机构） |
| 80 | donatePcFlg | int | 1 | 否 | 0 | 电脑端捐赠支持标志（0：不支持；1：支持） |

### 公益活动表 (业务管理系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | activityId | varchar | 32 |  |  | 活动id（act开头) |
| 3 | activityName | varchar | 20 | 否 |  | 活动名称 |
| 4 | subjectPicId | int | 11 |  |  | 主题图片ID，列表页面（主题图片比例为8：9） |
| 5 | subjectBigPicId | int | 11 |  |  | 主题图片ID，详情页面（主题图片比例为4:3） |
| 6 | activityIntroduction | text |  |  |  | 活动简介 |
| 7 | startTime | datetime |  |  |  | 活动开始时间 |
| 8 | endTime | datetime |  |  |  | 活动结束时间 |
| 9 | keywords1 | int | 11 |  |  | 关键字1 |
| 10 | keywords2 | int | 11 |  |  | 关键字2 |
| 11 | keywords3 | int | 11 |  |  | 关键字3 |
| 12 | keywords4 | int | 11 |  |  | 关键字4 |
| 13 | keywords5 | int | 11 |  |  | 关键字5 |
| 14 | status | int | 1 | 否 | 0 | 活动状态（0：未申请；1：在线申请中；2：在线上（审批通过）；3：中途取消；4：线下活动；5：审核不通过；6：已结束；7：测试发布；8：已删除；9：第三方平台（联劝网）） |
| 15 | institutionId | int | 11 | 否 |  | 机构ID |
| 16 | reviewDetail | text |  |  |  | 审核不通过的理由 |
| 17 | reviewTime | datetime |  |  |  | 审核时间 |
| 18 | reviewer | varchar | 32 |  |  | 审核者（用户名） |
| 19 | reviewNickName | varchar | 32 |  |  | 审核者昵称 |
| 20 | applyTime | datetime |  |  |  | 申请时间 |
| 21 | fundraisingAccount | varchar | 32 |  |  | 筹款账户 |
| 22 | fundraisingStartTime | datetime |  |  |  | 筹款开始时间 |
| 23 | fundraisingEndTime | datetime |  |  |  | 筹款截止时间 |
| 24 | isHaveWebSite | int | 1 | 否 | 1 | 是否有独立网站（0：有独立网站；1：没有独立网站）_x000D_ 有独立网站时，经由独立网站完成报名捐赠；_x000D_ 没有独立网站时，经由联劝的报名网站完成报名捐赠； |
| 25 | webSiteUrl | varchar | 500 |  |  | 独立网站访问地址（PC版） |
| 26 | webSiteUrlM | varchar | 200 |  |  | 独立网站访问地址（PAD版、手机版） |
| 27 | showTime | datetime |  |  |  | 活动线上展示时间 |
| 28 | useAds | int | 1 | 否 | 0 | 报名类型（0：由报名捐赠系统实现报名捐赠；1：不需要报名捐赠系统实现；2：由报名捐赠系统实现捐赠0：组队报名；1：不需要报名；2：个人报名；99：义卖活动） |
| 29 | createTime | datetime |  |  |  | 活动创建时间 |
| 30 | anonymousAccount | varchar | 32 |  |  | 匿名捐赠账户 |
| 31 | drawAccount | varchar | 32 |  |  | 抽签费账户 |
| 32 | registrationAccount | varchar | 32 |  |  | 报名费账号（要在线报名并需要支付报名费时为必须项，审批通过时开通） |
| 33 | version | varchar | 10 | 否 |  | 爱扑满版本号 |
| 34 | businessid | varchar | 32 | 否 |  | 感谢卡模板id |
| 35 | closeReanson | varchar | 200 |  |  | 取消理由 |
| 36 | closeTime | datetime |  |  |  | 取消时间 |
| 37 | closer | varchar | 32 |  |  | 取消者（机构名称或管理员用户名） |
| 38 | target1 | decimal | 15,2 |  | 20 | 筹款标的1 |
| 39 | target2 | decimal | 15,2 |  | 50 | 筹款标的2 |
| 40 | target3 | decimal | 15,2 |  | 100 | 筹款标的3 |
| 41 | indexShowOrder | int | 11 |  |  | 首页显示顺 |
| 42 | holderId | int | 11 |  |  | 所属ID（填写所属基金ID、所属专项合作ID） |
| 43 | type | int | 1 | 否 | 1 | 所属类型（1：独立活动；2：基金；3：合作；4：潜在资助机构；5：公募基金…） |
| 44 | pointTo | int | 1 | 否 |  | 用款指向（1：专款专用；2：无指向性） |
| 45 | snakerOrderId | varchar | 32 |  |  | 流程实例ID |
| 46 | snakerStepName | varchar | 100 |  |  | 当前任务名 |
| 47 | snakerStatus | varchar | 100 |  |  | 当前状态 |
| 48 | adminFeeType | tinyint | 1 |  |  | 行政管理费提取类型（0：不提取1：固定比例提取2：累进金额按约定比例提取） |
| 49 | adminFeeFixedRate | decimal | 5,2 |  |  | 行政管理费固定提取比例(非固定比例为空) |
| 50 | checkCode | varchar | 32 |  |  | 校验码 |
| 51 | codeInvalidTime | datetime |  |  |  | 校验码无效时间 |
| 52 | cycleEndTime | datetime |  |  |  | 活动最终结束时间 |
| 53 | mailjobreviewId | int | 11 |  |  | 活动反馈邮件任务ID（0：未生成；-1：已删除；-2：生成失败；-3：邮件组不存在；>0：已生成） |
| 54 | contacts | varchar | 100 |  |  | 主办方联系方式/客服电话 |
| 55 | ifNeedPhoneAM | int | 1 | 否 | 0 | 匿名捐赠是否必填手机（0：非必填；1：必填） |
| 56 | ifNeedEmailAM | int | 1 | 否 | 0 | 匿名捐赠是否必填邮箱（0：非必填；1：必填） |
| 57 | ifNeedFund | int | 1 | 否 | 0 | 是否需要筹款（0：要筹款；1：不要筹款） |
| 58 | videoId | varchar | 32 |  |  | 视频vid |
| 59 | showVideoFlag | int | 1 |  |  | 是否显示视频（0：不显示；1：显示） |
| 60 | recordNumber | varchar | 255 |  |  | 备案编号 |
| 61 | supportEn | int | 1 | 否 | 0 | 是否支持英文版（0：不支持；1：支持） |
| 62 | adminFeeFixedRateRq | decimal | 5,2 |  |  | 报名费行政管理费固定提取比例(非固定比例为空or0) |
| 63 | logisticalAccount | varchar | 32 |  |  | 快递费账户（义买活动时存在） |
| 64 | publishPlatformId | varchar | 64 |  |  | 项目发布平台ID(为空时，联劝网线上发布) |
| 65 | webSite | int | 1 |  |  | 筹款网址 |
| 66 | anonymousLotteryFeeAccount | varchar | 32 |  |  | 匿名抽签费账户 |
| 67 | anonymousRegistrationFeeAccount | varchar | 32 |  |  | 匿名报名费账户 |
| 68 | firstActivityName | varchar | 255 |  |  | 第一次录入的项目名称 |
| 69 | mark | int | 11 |  |  | 筹款是否已结束的标志位（0：未筹款；1：筹款中；2：筹款结束；） |
| 70 | flg | int | 1 |  | 1 | 新增第三方产品时用（1：联劝网活动；2：联劝网义买；） |
| 71 | charger | varchar | 32 |  |  | 活动负责人 |
| 72 | fundraisingMaxAmount | decimal | 15,2 |  |  | 筹款目标（根据项目筹款结束方式设置，可以为空） |

### 公益活动编辑表 (HORN系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | activityId | varchar | 32 |  |  | 活动id（act开头) |
| 3 | activityName | varchar | 20 |  |  | 活动名称 |
| 4 | subjectPicId | int | 11 |  |  | 主题图片ID，列表页面（主题图片比例为8：9） |
| 5 | subjectBigPicId | int | 11 |  |  | 主题图片ID，详情页面（主题图片比例为4:3） |
| 6 | activityIntroduction | text |  |  |  | 活动简介 |
| 7 | startTime | datetime |  |  |  | 活动开始时间 |
| 8 | endTime | datetime |  |  |  | 活动结束时间 |
| 9 | status | int | 1 |  |  | 编辑状态（0：公募修改中；1：公募提交审核；2：机构修改中；3：机构提交审核；5：公募审核不通过；6：机构审核不通过） |
| 10 | fundraisingAccount | varchar | 32 |  |  | 筹款账户 |
| 11 | fundraisingStartTime | datetime |  |  |  | 筹款开始时间 |
| 12 | fundraisingEndTime | datetime |  |  |  | 筹款截止时间 |
| 13 | anonymousAccount | varchar | 32 |  |  | 匿名捐赠账户 |
| 14 | institutionId | int | 11 |  |  | 机构ID |
| 15 | reviewDetail | text |  |  |  | 审核不通过的理由 |
| 16 | reviewTime | datetime |  |  |  | 审核时间 |
| 17 | reviewer | varchar | 32 |  |  | 审核者（用户名） |
| 18 | reviewNickName | varchar | 32 |  |  | 审核者昵称 |
| 19 | applyTime | datetime |  |  |  | 申请时间 |
| 20 | isHaveWebSite | int | 1 |  |  | 是否有独立网站（0：有独立网站；1：没有独立网站） |
| 21 | webSiteUrl | varchar | 500 |  |  | 独立网站访问地址（PC版） |
| 22 | webSiteUrlM | varchar | 200 |  |  | 独立网站访问地址（PAD版、手机版） |
| 23 | showTime | datetime |  |  |  | 活动线上展示时间 |
| 24 | useAds | int | 1 |  |  | 报名类型（ 0：组队报名；1：不需要报名；2：个人报名；99：义卖活动） |
| 25 | createTime | datetime |  |  |  | 活动创建时间 |
| 26 | registrationAccount | varchar | 32 |  |  | 报名费账号（要在线报名并需要支付报名费时为必须项，审批通过时开通） |
| 27 | drawAccount | varchar | 32 |  |  | 抽签费账户 |
| 28 | version | varchar | 10 |  |  | 爱扑满版本号 |
| 29 | businessid | varchar | 32 |  |  | 感谢卡模板id |
| 30 | closer | varchar | 32 |  |  | 取消者（机构名称或管理员用户名） |
| 31 | closeReanson | varchar | 200 |  |  | 取消理由 |
| 32 | closeTime | datetime |  |  |  | 取消时间 |
| 33 | target1 | decimal | 15,2 |  |  | 筹款标的1 |
| 34 | target2 | decimal | 15,2 |  |  | 筹款标的2 |
| 35 | target3 | decimal | 15,2 |  |  | 筹款标的3 |
| 36 | indexShowOrder | int | 11 |  |  | 首页显示顺 |
| 37 | holderId | varvhar | 11 |  |  | 所属ID（认领公募基金会GUID） |
| 38 | claimStatus | int | 1 |  |  | 认领状态（0：未认领；1：已认领；2：自己发起（公募基金专用）） |
| 39 | contacts | varchar | 100 |  |  | 主办方联系方式/客服电话 |
| 40 | ifNeedFund | int | 1 |  |  | 是否需要筹款（0：要筹款；1：不要筹款） |
| 41 | showVideoFlag | int | 1 |  |  | 是否显示视频（0：不显示；1：显示） |
| 42 | videoId | String | 32 |  |  | 视频vid |
| 43 | supportEn | int | 1 |  |  | 是否支持英文版（0：不支持；1：支持） |
| 44 | recordNumber | varchar | 255 |  |  | 备案编号 |
| 45 | logisticalAccount | varchar | 32 |  |  | 快递费账户（义买活动时存在） |
| 46 | lastEditStep | int | 2,0 |  |  | 上次关闭前最后修改的步骤 |
| 47 | fundraiseEntityGuid | varchar | 255 |  |  | 支付信息实体id |
| 48 | currentAccountType | int | 1 |  | 0 | 当前账户类型（0：未开；1：测试；2：正式） |
| 49 | isCollect | int | 1 |  | 0 | 是否收集用户信息 0不收集；1:收集； |
| 50 | lianQuanShowType | int | 1 |  |  | 在联劝网上的显示方式（0：慈善募捐；1：个人求助） |
| 51 | applyConfigTime | datetime |  |  |  | 申请配置的时间 |
| 52 | ifNeedActivityFund | int | 1 |  |  | 是否需要直捐给活动（0：需要；1：不需要；2：需要且初期为直捐活动） |
| 53 | details | varchar | 255 |  |  |  |
| 54 | venue | varchar | 255 |  |  |  |
| 55 | isMatch | int | 1 |  | 0 | 是否为需要配捐的活动（0：否；1：是） |
| 56 | ifNeedInsurance | int | 1 |  | 0 | 是否购买保险，0不购买，1购买 |
| 57 | updateTime | datetime |  |  |  | 更新时间 |
| 58 | numStartTime | date | 0 |  |  | 备案编号开始时间 |
| 59 | numEndTime | date | 0 |  |  | 备案编号结束时间 |
| 60 | numDetailUrl | date | 0 |  |  | 备案编号详情网址 |
| 61 | targetStr1 | varchar | 15 |  |  | 筹款标的1（别名） |
| 62 | targetStr1 | varchar | 15 |  |  | 筹款标的2（别名） |
| 63 | targetStr1 | varchar | 15 |  |  | 筹款标的2（别名） |
| 64 | targetflg | int | 1 |  | 0 | 标的别名标志（0：不使用；1：使用） |
| 65 | isNeedCom | int | 1 | 否 | 0 | 是否需要商品义卖（0：否；1：是） |
| 66 | fundraisingMoney | decimal | 15,2 |  | 0 | 资金同步筹款金额 |
| 67 | fundraisingCount | int | 11 |  | 0 | 资金同步筹款笔数 |
| 68 | fundMatch | decimal | 15,2 |  | 0 | 企业配捐金额 |
| 69 | countMatch | int | 11 |  | 0 | 企业配捐笔数 |
| 70 | amount | decimal | 15,2 | 是 | 0 | 预算总金额 |
| 71 | portraitFileId | int |  |  |  | 未成年人肖像协议文件 |
| 72 | coverage | varchar | 1024 |  |  | 活动所在地域（地域ID的组合，用,连接。业务侧新增加的活动不允许为空） |


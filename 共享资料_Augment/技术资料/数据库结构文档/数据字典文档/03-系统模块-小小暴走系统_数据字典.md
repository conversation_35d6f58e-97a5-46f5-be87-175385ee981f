# 小小暴走系统 数据字典

## 系统概述

**系统名称**: 小小暴走系统
**系统标识**: xxbz  
**文件来源**: xxbz_dataBaseModelDesign.xls  
**工作表总数**: 52  
**有效表数量**: 51  
**字段总数**: 616  

## 表结构清单

| 序号 | 表名 | 字段数 | 说明 |
|------|------|--------|------|
| 1 | t_xxbz_newFamily | 37 |  |
| 2 | t_xxbz_activity | 30 |  |
| 3 | t_xxbz_user | 28 |  |
| 4 | t_xxbz_family | 24 |  |
| 5 | t_xxbz_join_queue | 21 |  |
| 6 | t_xxbz_family_type | 21 |  |
| 7 | t_xxbz_team | 20 |  |
| 8 | t_xxbz_enterprise | 19 |  |
| 9 | t_xxbz_activity_enterprise | 19 |  |
| 10 | t_xxbz_family_member | 19 |  |
| 11 | t_xxbz_familyInvitation  | 19 |  |
| 12 | t_xxbz_teamtype | 18 |  |
| 13 | t_xxbz_news（旧版） | 18 |  |
| 14 | t_xxbz_invitation | 17 |  |
| 15 | 更新履历 | 16 |  |
| 16 | t_xxbz_certificate_file | 15 |  |
| 17 | t_xxbz_activityterm | 14 |  |
| 18 | t_xxbz_sms_template | 13 |  |
| 19 | t_horn_certificate_property | 13 |  |
| 20 | t_xxbz_invoice_temp_info | 13 |  |
| 21 | t_xxbz_show_pictures | 12 |  |
| 22 | t_xxbz_message（删除） | 12 |  |
| 23 | t_xxbz_smsRecord | 11 |  |
| 24 | t_xxbz_certificate_template | 11 |  |
| 25 | t_xxbz_pre_join_info | 11 |  |
| 26 | t_xxbz_reg_order | 11 |  |
| 27 | t_xxbz_user_family | 9 |  |
| 28 | t_xxbz_family_team | 9 |  |
| 29 | t_xxbz_family_video | 9 |  |
| 30 | t_xxbz_reg_order_sub | 9 |  |
| 31 | t_xxbz_family_match | 8 |  |
| 32 | t_xxbz_pictures | 8 |  |
| 33 | t_xxbz_funduse_detail | 8 |  |
| 34 | t_xxbz_family_video_likes | 8 |  |
| 35 | t_xxbz_news | 8 |  |
| 36 | t_xxbz_partake | 8 |  |
| 37 | 视图更新履历 | 7 |  |
| 38 | t_xxbz_message | 7 |  |
| 39 | t_xxbz_donationtype | 6 |  |
| 40 | t_xxbz_briefMeeting | 5 |  |
| 41 | t_xxbz_invitation_tip | 5 |  |
| 42 | t_xxbz_fund_use | 5 |  |
| 43 | t_xxbz_webconfig | 5 |  |
| 44 | t_xxbz_like_status | 5 |  |
| 45 | t_xxbz_activity_user | 4 |  |
| 46 | t_xxbz_termtype | 4 |  |
| 47 | t_xxbz_activityterm_apply | 4 |  |
| 48 | t_xxbz_banwords | 4 |  |
| 49 | t_xxbz_keywords | 3 |  |
| 50 | t_xxbz_source | 3 |  |
| 51 | t_xxbz_family_pictures | 3 |  |

## 主要表结构详情

### t_xxbz_newFamily

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 10 | 否 |  | 自动编号 |
| 2 | activityId | varchar | 18 | 否 |  | 活动编号 |
| 3 | familyId | varchar | 18 | 否 |  | 家庭编号（格式：F+YYYYMMDD24Hmmsssss） |
| 4 | familyName | varchar | 40 | 否 |  | 家庭名称 |
| 5 | createTime | datetime |  | 否 |  | 家庭组建时间 |
| 6 | fid | varchar | 16 | 是 |  | 家庭ID |
| 7 | pictureId | int | 11 | 是 |  | 头像ID |
| 8 | familyManifesto | varchar | 50 | 否 |  | 家庭口号 |
| 9 | raiseTarget | decimal | 10,2 | 否 |  | 筹款目标 |
| 10 | fundraisingAccount | varchar | 32 | 是 |  | 家庭筹款账户（临时） |
| 11 | familyEditTimeEnd | datetime |  | 是 |  | 家庭编辑结束时间（YYYY/MM/DD hh24:mi:ss） |
| 12 | uid | varchar | 16 | 否 |  | 组建人Uid |
| 13 | contactUserName | varchar | 32 | 是 |  | 联系人姓名 |
| 14 | contactPhone | varchar | 11 | 是 |  | 联系人手机 |
| 15 | contactEmail | varchar | 48 | 是 |  | 联系人邮箱 |
| 16 | paymentStatus | tinyint | 1 | 否 | 0 | 报名费缴费状态（0：未支付；1：已支付；） |
| 17 | paymentTime | datetime |  | 是 |  | 报名费缴费时间 |
| 18 | memberNum | int | 11 | 否 | 0 | 成员数 |
| 19 | invitationCode | varchar | 16 | 是 |  | 邀请码 |
| 20 | familyStatus | int | 2 | 否 |  | 家庭状态（0：正常；1：解散；2：待审核 3:审核不通过） |
| 21 | familyTypeId | int | 2 | 否 |  | 家庭类型编号 |
| 22 | registrationAccount | varchar | 32 | 是 |  | 家庭报名费账户（临时） |
| 23 | familyDonation | decimal | 10,2 | 是 |  | 家庭筹款额，报名费转筹款额金额 |
| 24 | applyReason | varchar | 60 | 是 |  | 审核理由 |
| 25 | meetingId | int | 11 | 是 |  | 碰头会场次id |
| 26 | meetingNumber | int | 11 | 否 | 0 | 参加碰头会人数 |
| 27 | pfPicId | int | 10 | 是 |  | 先锋家庭的图片id |
| 28 | isOss | int | 1 | 是 | 0 | 是否oss加速 0：不加速；1：加速 |
| 29 | isOss2 | int | 1 | 是 | 0 | 是否oss加速 0：不加速；1：加速 （头像图片） |
| 30 | ossUrl2 | varchar | 120 | 是 |  | oss图片url（头像图片） |
| 31 | DAFActivityId | varchar | 32 | 是 |  | DAF活动编号 |
| 32 | ralateTime | datetime | 0 | 是 |  | 关联时间 |
| 33 | initAmount | decimal | 10 | 是 |  | DAF关联时筹款金额 |
| 34 | DAFFundraisingAccount | varchar | 32 | 是 |  | DAF筹款账户 |
| 35 | ifContract | int | 1 | 是 |  | 是否签约（先锋家庭使用）（0：未签约；1：已签约） |
| 36 | preJoinStatus | tinyint | 1 | 否 | 0 | 定金缴纳状态（0：未支付；1：已支付；） |
| 37 | initCount | int | 11 | 是 |  | DAF关联时筹款笔数 |

### t_xxbz_activity

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 10 | 否 |  | 自动编号 |
| 2 | activityId | varchar | 18 | 否 |  | 活动编号 |
| 3 | year | varchar | 4 | 否 |  | 年度 |
| 4 | activitySubject | varchar | 20 | 否 |  | 活动主题 |
| 5 | activityContent | varchar | 50 | 是 |  | 活动详细内容 |
| 6 | activityTimeStart | datetime |  | 否 |  | 活动启动时间（YYYY/MM/DD hh24:mi:ss） |
| 7 | activityTimeEnd | datetime |  | 否 |  | 活动结束时间（YYYY/MM/DD hh24:mi:ss） |
| 8 | applyTimeStart | datetime |  | 否 |  | 活动报名启动时间（YYYY/MM/DD hh24:mi:ss） |
| 9 | applyTimeEnd | datetime |  | 否 |  | 活动报名结束时间（YYYY/MM/DD hh24:mi:ss） |
| 10 | currentActivityFlag | int | 2 | 否 |  | 当前活动标志（0：非当前活动 1：当前活动） |
| 11 | fundraisingAccount | varchar | 32 | 否 |  | 活动筹款账户（临时） |
| 12 | registrationAccount | varchar | 32 | 否 |  | 活动报名费账户（临时） |
| 13 | baozouDate | datetime |  | 是 |  | 暴走日（YYYY/MM/DD hh24:mi:ss） |
| 14 | fundraisingEndDate | datetime |  | 是 |  | 筹款结束时间（YYYY/MM/DD hh24:mi:ss） |
| 15 | targetLabel1 | decimal | 10,2 | 否 |  | 筹款目标标的1 |
| 16 | targetLabel2 | decimal | 10,2 | 否 |  | 筹款目标标的2 |
| 17 | targetLabel3 | decimal | 10,2 | 否 |  | 筹款目标标的3 |
| 18 | teamEditTimeEnd | datetime |  | 是 |  | 队伍编辑结束时间（YYYY/MM/DD hh24:mi:ss） |
| 19 | familyEditTimeEnd | datetime |  | 是 |  | 家庭编辑结束时间（YYYY/MM/DD hh24:mi:ss） |
| 20 | anonymousAccount | varchar | 32 | 否 |  | 匿名账户 |
| 21 | venues | varchar | 50 | 是 |  | 活动地点 |
| 22 | reqistrationFam | decimal | 10,2 | 否 |  | 每个家庭的报名费金额 |
| 23 | signUpType | int | 2 | 否 | 1 | 报名通道区分 【0：战队/家庭；1：家庭】 |
| 24 | preJoinStart | datetime |  | 是 |  | 预报名启动时间（YYYY/MM/DD hh24:mi:ss） |
| 25 | preJoinEnd | datetime |  | 是 |  | 预报名结束时间（YYYY/MM/DD hh24:mi:ss） |
| 26 | preJoinTailStart | datetime |  | 是 |  | 尾款支付开始时间（YYYY/MM/DD hh24:mi:ss） |
| 27 | preJoinReqistration | decimal | 10,2 | 是 | 50.00 | 预报名缴费金额 |
| 28 | preJoinFlag | int | 1 | 否 | 0 | 是否开启预报名（0：不开启 1：开启） |
| 29 | preJoinDeduction | decimal | 10,2 | 是 |  | 预报名抵扣金额 |
| 30 | preJoinInvalid | datetime |  | 是 |  | 预报名资格失效时间（YYYY/MM/DD hh24:mi:ss） |

### t_xxbz_user

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号 |
| 2 | userId | varchar | 30 | 是 |  | 用户名 |
| 3 | userName | varchar | 20 | 是 |  | 用户真实姓名 |
| 4 | nickName | varchar | 30 | 是 |  | 用户昵称 |
| 5 | birth | datetime |  | 是 |  | 生日 |
| 6 | identityCard | varchar | 18 | 是 |  | 身份证号/台胞证 |
| 7 | phone | varchar | 11 | 是 |  | 手机 |
| 8 | email | varchar | 48 | 是 |  | 邮箱 |
| 9 | pictureId | varchar | 30 | 是 |  | 头像GUID |
| 10 | donateAccount | varchar | 32 | 是 |  | 用户捐款账户（永久） |
| 11 | uid | varchar | 16 | 否 |  | 用户ID(对外公开用) |
| 12 | nationality | tinyint | 1 | 是 |  | 国籍（0：中国大陆；1：港澳及外籍人士；2：台湾同胞） |
| 13 | passport | varchar | 18 | 是 |  | 护照号 |
| 14 | sex | tinyint | 1 | 是 |  | 性别（0：男；1女） |
| 15 | contactEmail | varchar | 48 | 是 |  | 联系邮箱 |
| 16 | workUnit | varchar | 255 | 是 |  | 工作单位 |
| 17 | position | varchar | 255 | 是 |  | 工作职位 |
| 18 | nationalityDetail | varchar | 255 | 是 |  | 国籍详细 |
| 19 | hukouProvince | int | 11 | 是 |  | 户籍地址的省或直辖市的地名ID |
| 20 | hukouProvinceNm | varchar | 32 | 是 |  | 户籍地址的省或直辖市的地名 |
| 21 | hukouCity | int | 11 | 是 |  | 户籍地址的市ID |
| 22 | hukouCityNm | varchar | 32 | 是 |  | 户籍地址的市 |
| 23 | hukouAddress | varchar | 255 | 是 |  | 户籍地址的详细地址 |
| 24 | province | int | 11 | 是 |  | 住址的省或直辖市的地名ID |
| 25 | provinceNm | varchar | 32 | 是 |  | 住址的省或直辖市的地名 |
| 26 | city | int | 11 | 是 |  | 住址的市ID |
| 27 | cityNm | varchar | 32 | 是 |  | 住址的市 |
| 28 | detailAddress | varchar | 255 | 是 |  | 住址的详细地址 |

### t_xxbz_family

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 10 | 否 |  | 自动编号 |
| 2 | familyId | varchar | 18 | 否 |  | 家庭编号 |
| 3 | familyName | varchar | 40 | 否 |  | 家庭名称 |
| 4 | createTime | datetime |  | 否 |  | 家庭组建时间 |
| 5 | fid | varchar | 16 | 是 |  | 家庭ID |
| 6 | pictureId | int | 11 | 是 |  | 头像ID |
| 7 | familyManifesto | varchar | 50 | 否 |  | 家庭口号 |
| 8 | raiseTarget | decimal | 10,2 | 否 |  | 筹款目标 |
| 9 | matchAccount | varchar | 32 | 是 |  | 配比账户（临时） |
| 10 | fundraisingAccount | varchar | 32 | 是 |  | 家庭筹款账户（临时） |
| 11 | teamId | varchar | 18 | 是 |  | 队伍编号 |
| 12 | activityId | varchar | 18 | 否 |  | 活动编号 |
| 13 | familyEditTimeEnd | datetime |  | 是 |  | 家庭编辑结束时间（YYYY/MM/DD hh24:mi:ss） |
| 14 | uid | varchar | 16 | 否 |  | 联系人Uid |
| 15 | province | int | 11 | 是 |  | 家庭住址的省或直辖市的地名ID |
| 16 | provinceNm | varchar | 32 | 是 |  | 家庭住址的省或直辖市的地名 |
| 17 | city | int | 11 | 是 |  | 家庭住址的市ID |
| 18 | cityNm | varchar | 32 | 是 |  | 家庭住址的市 |
| 19 | detailAddress | varchar | 255 | 是 |  | 家庭住址的详细地址 |
| 20 | majorChild | varchar | 16 | 是 |  | 主要儿童uid |
| 21 | contactUserName | varchar | 32 | 是 |  | 联系人姓名 |
| 22 | contactPhone | varchar | 11 | 是 |  | 联系人手机 |
| 23 | contactEmail | varchar | 48 | 是 |  | 联系人邮箱 |
| 24 | promoCode | varchar | 32 | 是 |  | 推广码（32位以内的随机数字） |

### t_xxbz_join_queue

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 10 | 否 |  | 自动编号 |
| 2 | activityId | varchar | 18 | 否 |  | 活动编号 |
| 3 | joinFlag | tinyint | 1 | 否 |  | 组建类型（1：团体 2：家庭） |
| 4 | invitationCode | varchar | 10 | 是 |  | 邀请码 |
| 5 | familyTypeId | int | 2 | 否 |  | 家庭类型编号【0：社群团体；1：公益自筹家庭；3：公众家庭;4:先锋家庭】 |
| 6 | createTime | datetime |  | 否 |  | 生成时间 |
| 7 | status | tinyint | 1 | 否 | 0 | 状态（0：未处理；1：已处理；2：错误信息等待查看；3：组建失败） |
| 8 | errMsg | varchar | 255 | 是 |  | 错误信息 |
| 9 | groupName | varchar | 32 | 是 |  | 机构或企业名称 |
| 10 | familysMax | int | 2 | 是 |  | 允许创建的最大家庭数 |
| 11 | manifesto | varchar | 200 | 是 |  | 口号 |
| 12 | uid | varchar | 16 | 否 |  | 所有人Uid |
| 13 | contactUserName | varchar | 32 | 是 |  | 联系人姓名 |
| 14 | contactPhone | varchar | 11 | 是 |  | 联系人手机 |
| 15 | contactEmail | varchar | 48 | 是 |  | 联系人邮箱 |
| 16 | familyId | varchar | 18 | 否 |  | 家庭编号（格式：F+YYYYMMDD24Hmmsssss） |
| 17 | familyName | varchar | 40 | 否 |  | 家庭名称 |
| 18 | pfPicId | int | 10 | 是 |  | 先锋家庭的图片id |
| 19 | videoUrl | varchar | 1024 | 否 |  | 视频地址 |
| 20 | sourceUserId | varchar | 32 | 是 |  | 推广人用户名 |
| 21 | creatorUid | varchar | 16 | 否 |  | 组建人Uid |

### t_xxbz_family_type

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 10 | 否 |  | 自动编号 |
| 2 | familyTypeId | int | 2 | 否 |  | 家庭类型编号 |
| 3 | familyTypeName | varchar | 40 | 否 |  | 家庭类型名称 |
| 4 | playersAdultMin | int | 2 | 否 |  | 家庭允许加入队员（成人）最少人数 |
| 5 | playersAdultMax | int | 2 | 否 |  | 家庭允许加入队员（成人）最多人数 |
| 6 | playersChildMin | int | 2 | 否 |  | 家庭允许加入队员（小孩）最少人数 |
| 7 | playersChildMax | int | 2 | 否 |  | 家庭允许加入队员（小孩）最多人数 |
| 8 | explainInfo | text | 0 | 是 |  | 说明（关于该队伍类型的补充说明） |
| 9 | activityId | varchar | 18 | 否 |  | 活动编号 |
| 10 | reqistration | decimal | 10,2 | 否 |  | 报名费 |
| 11 | raiseTarget | decimal | 10,2 | 否 |  | 筹款目标 |
| 12 | status | int | 1 | 否 | 0 | 状态（0：显示 1：不显示） |
| 13 | reqistrationDonation | decimal | 10,2 | 否 |  | 报名费捐赠额 |
| 14 | message | text | 0 | 是 |  | 提示信息 |
| 15 | maxFamilyNum | int | 3 | 否 |  | 同一个类型允许的最大家庭数量 |
| 16 | reqistrationAll | decimal | 10,2 | 否 |  | 报名费总额（按家庭） |
| 17 | applyTimeStart | datetime |  | 是 |  | 报名开始时间 |
| 18 | applyTimeEnd | datetime |  | 是 |  | 报名结束时间 |
| 19 | applyTimeAfter | datetime |  | 是 |  | 报名费早鸟价结束时间 |
| 20 | reqistrationAfter | decimal | 10,2 | 否 |  | 正常报名费 |
| 21 | type | int | 1 | 是 |  | 类型（0：团体；1：家庭；2：先锋家庭（DFA活动）） |

### t_xxbz_team

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 10 | 否 |  | 自动编号 |
| 2 | teamId | varchar | 18 | 否 |  | 队伍编号 |
| 3 | teamName | varchar | 40 | 否 |  | 队伍名称 |
| 4 | createTime | datetime |  | 否 |  | 队伍组建时间 |
| 5 | tid | varchar | 16 | 否 |  | 队伍ID |
| 6 | pictureId | int | 11 | 是 |  | 头像ID |
| 7 | teamManifesto | varchar | 50 | 否 |  | 队伍宣言 |
| 8 | paymentStatus | tinyint | 1 | 否 | 0 | 报名费缴费状态（0：未支付；1：已支付；） |
| 9 | paymentTime | datetime |  | 是 |  | 报名费缴费时间 |
| 10 | invitationCode | varchar | 16 | 是 |  | 邀请码 |
| 11 | fundraisingAccount | varchar | 32 | 是 |  | 队伍筹款账户（临时） |
| 12 | RegistrationAccount | varchar | 32 | 是 |  | 队伍报名费账户（临时） |
| 13 | teamStatus | tinyint | 1 | 否 |  | 队伍状态（0：正常；1：解散；2：待审核） |
| 14 | uid | varchar | 16 | 否 |  | 联系人用户UID（即队长） |
| 15 | activityId | varchar | 18 | 否 |  | 活动编号 |
| 16 | teamEditTimeEnd | datetime |  | 是 |  | 队伍编辑结束时间（YYYY/MM/DD hh24:mi:ss） |
| 17 | familyNum | int | 11 | 否 | 0 | 家庭数 |
| 18 | raiseTarget | decimal | 15,2 | 否 | 0.00 | 筹款目标（家庭筹款目标的合计值） |
| 19 | institutionEntityGuid | varchar | 32 | 是 |  | 所属机构ID |
| 20 | promoCode | varchar | 32 | 是 |  | 推广码（32位以内的随机数字） |

### t_xxbz_enterprise

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 10 | 否 |  | 自动编号 |
| 2 | enterpriseId | varchar | 18 | 否 |  | 企业编号 |
| 3 | enterpriseName | varchar | 20 | 否 |  | 企业名称 |
| 4 | logopic | int | 11 | 否 |  | 企业logo |
| 5 | address | varchar | 50 | 是 |  | 企业地址 |
| 6 | aboutus | varchar | 200 | 是 |  | 企业简介 |
| 7 | uid | varchar | 30 | 是 |  | 企业联系人用户ID |
| 8 | postcode | varchar | 6 | 是 |  | 企业邮编 |
| 9 | tel | varchar | 20 | 是 |  | 企业总机 |
| 10 | fax | varchar | 20 | 是 |  | 企业传真 |
| 11 | email | varchar | 50 | 是 |  | 企业邮箱 |
| 12 | http | varchar | 100 | 是 |  | 官方网站 |
| 13 | videoLink | varchar | 100 | 是 |  | 视频链接 |
| 14 | remark | varchar | 200 | 是 |  | 备注 |
| 15 | whycarepic_guid | int | 11 | 是 |  | Why We Care的图片文件id |
| 16 | csrstorypic_guid | int | 11 | 是 |  | Our CSR Story的图片文件id |
| 17 | csrpic_guid | int | 11 | 是 |  | CSR的图片文件id |
| 18 | account | varchar | 32 | 是 |  | 企业账户（永久） |
| 19 | deleteFlag | tinyint | 1 | 否 | 0 | 企业删除标志（0：正常 1：删除） |

### t_xxbz_activity_enterprise

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 10 | 否 |  | 自动编号 |
| 2 | activityId | varchar | 18 | 否 |  | 活动编号 |
| 3 | enterpriseId | varchar | 18 | 否 |  | 企业编号 |
| 4 | enterpriseManifesto | varchar | 50 | 否 |  | 企业宣言 |
| 5 | donationtypeId | int | 2 | 否 |  | 赞助企业类型编号 |
| 6 | showOrder | int | 2 | 是 | 99 | 企业在页面上的显示顺序 |
| 7 | displaymark | tinyint | 1 | 否 |  | logo显示时企业名称是否显示  不显示：0；显示：1； |
| 8 | fundraisingAccount | varchar | 32 | 是 |  | 筹款账户（临时），用于企业配比 |
| 9 | registrationAccount | varchar | 32 | 是 |  | 报名费账户（临时），用于企业邀请码 |
| 10 | account1 | varchar | 32 | 是 |  | 配捐总账户（临时） |
| 11 | account2 | varchar | 32 | 是 |  | 配捐账户（临时） |
| 12 | account3 | varchar | 32 | 是 |  | 预留账户（临时） |
| 13 | account4 | varchar | 32 | 是 |  | 预留账户（临时） |
| 14 | account5 | varchar | 32 | 是 |  | 预留账户（临时） |
| 15 | account6 | varchar | 32 | 是 |  | 预留账户（临时） |
| 16 | account7 | varchar | 32 | 是 |  | 预留账户（临时） |
| 17 | account8 | varchar | 32 | 是 |  | 预留账户（临时） |
| 18 | account9 | varchar | 32 | 是 |  | 预留账户（临时） |
| 19 | account10 | varchar | 32 | 是 |  | 预留账户（临时） |

### t_xxbz_family_member

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 10 | 否 |  | 自动编号 |
| 2 | familyId | varchar | 18 | 否 |  | 所属家庭编号 |
| 3 | familyName | varchar | 40 | 否 |  | 所属家庭名称 |
| 4 | userName | varchar | 20 | 是 |  | 用户真实姓名 |
| 5 | nickName | varchar | 30 | 是 |  | 用户昵称 |
| 6 | birth | datetime |  | 是 |  | 生日 |
| 7 | identityCard | varchar | 18 | 是 |  | 身份证号/台胞证 |
| 8 | nationality | tinyint | 1 | 是 |  | 国籍（0：中国大陆；1：港澳及外籍人士；2：台湾同胞） |
| 9 | passport | varchar | 18 | 是 |  | 护照号 |
| 10 | sex | tinyint | 1 | 是 |  | 性别（0：男；1女） |
| 11 | nationalityDetail | varchar | 255 | 是 |  | 国籍详细 |
| 12 | hukouProvince | int | 11 | 是 |  | 家庭地址的省或直辖市的地名ID |
| 13 | hukouProvinceNm | varchar | 32 | 是 |  | 家庭地址的省或直辖市的地名 |
| 14 | hukouCity | int | 11 | 是 |  | 家庭地址的市ID |
| 15 | hukouCityNm | varchar | 32 | 是 |  | 家庭地址的市 |
| 16 | hukouAddress | varchar | 255 | 是 |  | 家庭地址的详细地址 |
| 17 | userType | tinyint | 1 | 否 |  | 队员类型（0：主要儿童；1：家长；2：正式队员） |
| 18 | userStatus | int | 1 | 否 |  | 用户状态（1：已加入） |
| 19 | addTime | Date |  | 否 |  | 加入时间 |


# 统计分析系统 数据字典

## 系统概述

**系统名称**: 统计分析系统  
**系统标识**: statistics  
**文件来源**: statistics_dataBaseModelDesign.xls  
**工作表总数**: 30  
**有效表数量**: 26  
**字段总数**: 352  

## 表结构清单

| 序号 | 表名 | 字段数 | 说明 |
|------|------|--------|------|
| 1 | 月捐签约人信息统计表 | 28 |  |
| 2 | 月捐签约人信息统计表（无暂停版） | 28 |  |
| 3 | 每日捐赠统计表 | 21 |  |
| 4 | 月捐每日统计表 | 21 |  |
| 5 | 月捐月度统计表 | 21 |  |
| 6 | 新增实名用户统计表 | 20 |  |
| 7 | 月捐人统计表 | 19 |  |
| 8 | 月捐解约行为统计表 | 18 |  |
| 9 | 月捐邀请日统计表 | 17 |  |
| 10 | 每月捐赠统计表 | 16 |  |
| 11 | 爱扑满日统计表 | 16 |  |
| 12 | 联劝网筹款产品表 | 14 |  |
| 13 | 月捐签约行为统计表 | 14 |  |
| 14 | 月捐扣款成功统计表 | 13 |  |
| 15 | 月捐签约金额变更统计表 | 11 |  |
| 16 | 月捐扣款失败统计表 | 11 |  |
| 17 | 月捐签约用户月份统计表 | 10 |  |
| 18 | 月捐签约用户月份信息表 | 9 |  |
| 19 | 发票统计表 | 8 |  |
| 20 | 月度24小时统计表 | 7 |  |
| 21 | 每月捐赠统计 | 7 |  |
| 22 | 月捐统计开始表 | 7 |  |
| 23 | 统计配置表 | 5 |  |
| 24 | 注册用户统计表 | 5 |  |
| 25 | 实体类型表 | 3 |  |
| 26 | 支付类型表 | 3 |  |

## 主要表结构详情

### 月捐签约人信息统计表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 0 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | username | varchar | 32 | 否 |  | 月捐人guid |
| 3 | contract_id | varchar | 32 |  |  | 月捐guid |
| 4 | contract_time | datetime | 0 |  |  | 最新签约时间 |
| 5 | contract_time_first | datetime | 0 |  |  | 第一次签约时间 |
| 6 | contract_num | int | 0 |  |  | 签约次数 |
| 7 | termination_type | int | 0 |  |  | 解约方式（0～10：微信侧解约 11：个人中心解约 12：个人中心修改月捐 13：联劝网解约 14：联劝网修改月捐 15：机构解约 16：boss管理员解约 17：连续扣款失败，自动解约 ） |
| 8 | termination_time | datetime | 0 |  |  | 最新解约时间 |
| 9 | termination_time_first | datetime | 0 |  |  | 第一次解约时间 |
| 10 | termination_num | int | 0 |  |  | 解约次数 |
| 11 | termination_remark | varchar | 1024 |  |  | 最新的解约备注 |
| 12 | status | int | 0 |  |  | 当前签约状态（0：未签约；1：已签约；2：已解约） |
| 13 | amount | double | 15 |  |  | 当前签约金额 |
| 14 | source | varchar | 32 |  |  | 当前月捐邀请guid |
| 15 | remark | varchar | 1024 |  |  | 当前签约留言 |
| 16 | contract_no | varchar | 32 |  |  | 个人月捐编号 |
| 17 | extension | varchar | 16 |  |  | 当前线下劝募渠道 |
| 18 | all_amount | double | 15 |  |  | 累计月捐金额 |
| 19 | all_count | int | 0 |  |  | 累计月捐笔数 |
| 20 | all_month | int | 0 | 否 |  | 累计月捐月份数 |
| 21 | from_account | varchar | 32 | 否 |  | 捐赠账户（用户账户） |
| 22 | to_account | varchar | 255 |  |  | 受捐账户（基金账户） |
| 23 | donate_date | date | 0 |  |  | 最近一次统计的捐赠月份 |
| 24 | fund_guid | varchar | 32 |  |  | 公募基金会实体GUID |
| 25 | ins_guid | varchar | 32 |  |  | 机构实体GUID |
| 26 | deduction_time | datetime | 0 |  |  | 最新扣款时间 |
| 27 | create_time | datetime | 0 |  |  | 创建时间 |
| 28 | update_time | datetime | 0 |  |  | 更新时间 |

### 月捐签约人信息统计表（无暂停版）

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 0 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | username | varchar | 32 | 否 |  | 月捐人guid |
| 3 | contract_id | varchar | 32 |  |  | 月捐guid |
| 4 | contract_time | datetime | 0 |  |  | 最新签约时间 |
| 5 | contract_time_first | datetime | 0 |  |  | 第一次签约时间 |
| 6 | contract_num | int | 0 |  |  | 签约次数 |
| 7 | termination_type | int | 0 |  |  | 解约方式（0～10：微信侧解约 11：个人中心解约 12：个人中心修改月捐 13：联劝网解约 14：联劝网修改月捐 15：机构解约 16：boss管理员解约 17：连续扣款失败，自动解约 ） |
| 8 | termination_time | datetime | 0 |  |  | 最新解约时间 |
| 9 | termination_time_first | datetime | 0 |  |  | 第一次解约时间 |
| 10 | termination_num | int | 0 |  |  | 解约次数 |
| 11 | termination_remark | varchar | 1024 |  |  | 最新的解约备注 |
| 12 | status | int | 0 |  |  | 当前签约状态（0：未签约；1：已签约；2：已解约） |
| 13 | amount | double | 15 |  |  | 当前签约金额 |
| 14 | source | varchar | 32 |  |  | 当前月捐邀请guid |
| 15 | remark | varchar | 1024 |  |  | 当前签约留言 |
| 16 | contract_no | varchar | 32 |  |  | 个人月捐编号 |
| 17 | extension | varchar | 16 |  |  | 当前线下劝募渠道 |
| 18 | all_amount | double | 15 |  |  | 累计月捐金额 |
| 19 | all_count | int | 0 |  |  | 累计月捐笔数 |
| 20 | all_month | int | 0 | 否 |  | 累计月捐月份数 |
| 21 | from_account | varchar | 32 | 否 |  | 捐赠账户（用户账户） |
| 22 | to_account | varchar | 255 |  |  | 受捐账户（基金账户） |
| 23 | donate_date | date | 0 |  |  | 最近一次统计的捐赠月份 |
| 24 | fund_guid | varchar | 32 |  |  | 公募基金会实体GUID |
| 25 | ins_guid | varchar | 32 |  |  | 机构实体GUID |
| 26 | deduction_time | datetime | 0 |  |  | 最新扣款时间 |
| 27 | create_time | datetime | 0 |  |  | 创建时间 |
| 28 | update_time | datetime | 0 |  |  | 更新时间 |

### 每日捐赠统计表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  | ID，自动递增 |
| 2 | count_day | date | 0 | 是 |  | 统计日 |
| 3 | username | varchar | 32 | 是 |  | 捐赠人 |
| 4 | count_type | int | 11 | 是 |  | 捐赠对象类型 |
| 5 | count_guid | varchar | 32 | 是 |  | 捐赠对象guid |
| 6 | count_name | varchar | 64 | 是 |  | 捐赠对象名称 |
| 7 | pool_type | int | 11 | 是 |  | 类型（3：活动；15：筹款产品；27：月捐；29：日捐） |
| 8 | pool_guid | varchar | 32 | 是 |  | 活动/项目/月捐/日捐guid |
| 9 | pool_name | varchar | 64 | 是 |  | 活动/项目/月捐/日捐名称 |
| 10 | ins_guid | varchar | 32 | 是 |  | 机构guid |
| 11 | ins_name | varchar | 64 | 是 |  | 机构名称 |
| 12 | fund_guid | varchar | 32 | 是 |  | 公募guid |
| 13 | fund_name | varchar | 64 | 是 |  | 公募名称 |
| 14 | order_type | int | 1 | 是 |  | 订单类型（0：报名费；1：捐款；5：抽签） |
| 15 | pay_type | int | 1 | 是 |  | 支付方式（1：支付宝；4：微信；8：移动和包；9：外部接口；99：配捐） |
| 16 | donate_type | int | 1 | 是 |  | 捐赠类型（0：普通捐赠；4：月捐；5：日捐） |
| 17 | access_type | int | 1 | 是 |  | 访问方式（0：WEB 1：MOBILE 2：管理员添加 3：第三方接口） |
| 18 | all_amount | decimal | 15，2 | 是 |  | 金额 |
| 19 | all_count | int | 11 | 是 |  | 笔数 |
| 20 | create_time | datetime | 0 | 是 |  | 创建时间 |
| 21 | update_time | datetime | 0 | 是 |  | 更新时间 |

### 月捐每日统计表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  | ID，自动增长 |
| 2 | count_day | date | 0 | 否 |  | 统计日 |
| 3 | contract_id | varchar | 32 | 否 |  | 月捐ID |
| 4 | contract_name | varchar |  | 是 |  | 月捐名称 |
| 5 | fund_guid | varchar | 32 | 是 |  | 公募guid |
| 6 | fund_name | varchar |  | 是 |  | 公募名称 |
| 7 | ins_guid | varchar | 32 | 是 |  | 机构guid |
| 8 | ins_name | varchar |  | 是 |  | 机构名称 |
| 9 | field_guid | varchar | 64 | 是 |  | 捐助领域guid |
| 10 | people_guid | varchar | 64 | 是 |  | 受助对象guid |
| 11 | contract_amount | decimal | 15 | 是 |  | 月捐金额 |
| 12 | contract_count | int | 0 | 是 |  | 月捐笔数 |
| 13 | contract_number | int | 0 | 是 |  | 新增签约人数 |
| 14 | termination_number | int | 0 | 是 |  | 新增解约人数 |
| 15 | growthNumber | int | 0 | 是 |  | 净增人数 |
| 16 | deductionNumber | int | 0 | 是 |  | 扣款数量 |
| 17 | all_amount | decimal | 15 | 是 |  | 累计金额 |
| 18 | all_count | int | 0 | 是 |  | 累计笔数 |
| 19 | all_contract_number | int | 0 | 是 |  | 累计签约中人数 |
| 20 | create_time | datetime | 0 | 是 |  | 创建时间 |
| 21 | update_time | datetime | 0 | 是 |  | 更新时间 |

### 月捐月度统计表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  | ID，自动增长 |
| 2 | count_month | date | 0 | 否 |  | 统计月（YYYY-MM-01） |
| 3 | contract_id | varchar | 32 | 否 |  | 月捐ID |
| 4 | contract_name | varchar |  | 是 |  | 月捐名称 |
| 5 | fund_guid | varchar | 32 | 是 |  | 公募guid |
| 6 | fund_name | varchar |  | 是 |  | 公募名称 |
| 7 | ins_guid | varchar | 32 | 是 |  | 机构guid |
| 8 | ins_name | varchar |  | 是 |  | 机构名称 |
| 9 | field_guid | varchar | 64 | 是 |  | 捐助领域guid |
| 10 | people_guid | varchar | 64 | 是 |  | 受助对象guid |
| 11 | contract_amount | decimal | 15 | 是 |  | 月捐金额 |
| 12 | contract_count | int | 0 | 是 |  | 月捐笔数 |
| 13 | contract_number | int | 0 | 是 |  | 新增签约人数 |
| 14 | termination_number | int | 0 | 是 |  | 新增解约人数 |
| 15 | growthNumber | int | 0 | 是 |  | 净增人数 |
| 16 | deductionNumber | int | 0 | 是 |  | 扣款数量 |
| 17 | all_amount | decimal | 15 | 是 |  | 累计金额 |
| 18 | all_count | int | 0 | 是 |  | 累计笔数 |
| 19 | all_contract_number | int | 0 | 是 |  | 累计签约中人数 |
| 20 | create_time | datetime | 0 | 是 |  | 创建时间 |
| 21 | update_time | datetime | 0 | 是 |  | 更新时间 |

### 新增实名用户统计表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 0 | 否 |  | ID，自动增长 |
| 2 | count_day | date | 0 | 否 |  | 统计日 |
| 3 | username | varchar | 32 |  |  | 用户名(统计时为实名用户的，必填） |
| 4 | register_time | datetime |  |  |  | 注册时间(统计时为实名用户的，必填） |
| 5 | phone | varchar | 11 | 否 |  | 手机 |
| 6 | weixin | varchar | 64 |  |  | 微信（openid，认证登录用） |
| 7 | type | int | 0 | 否 |  | 行为类型（1：注册；2：报名；3：捐赠；4：爱扑满领取；5：月捐邀请；6：开具发票；7：投诉建议；8：捐赠人评分；9：月捐签约；10：日捐签约；11：收藏） ——实名用户，只统计注册当日最早1次的行为 |
| 8 | entity_guid | varchar | 64 |  |  | 行为对象guid（活动、项目、月捐、日捐、义买活动、商品） |
| 9 | do_time | datetime | 0 |  |  | 行为时间 |
| 10 | holder_guid | varchar | 32 |  |  | 行为对象所属公募机构guid |
| 11 | institution_guid | varchar | 32 |  |  | 行为对象所属执行机构guid |
| 12 | is_third_form | int | 0 |  |  | 是否第三方平台的捐赠同步用户（0：是 1：否） |
| 13 | collect_name | varchar | 64 |  |  | 收藏的活动、项目、月捐、日捐、商品名称（只有行为为收藏时才统计） |
| 14 | to_register | int | 0 | 否 |  | 是否转注册（0：转注册 1：未注册；2：已注册） |
| 15 | to_register_time | datetime | 0 |  |  | 转注册时间 |
| 16 | to_register_type | int | 0 |  |  | 转注册的行为类型（1：注册；2：报名；3：捐赠；4：爱扑满领取；5：月捐邀请；6：开具发票；7：投诉建议；8：捐赠人评分；9：月捐签约；10：日捐签约；11：收藏） ——只统计匿名捐赠用户转注册当日最 |
| 17 | to_register_entity_guid | varchar | 64 |  |  | 转注册行为对象guid（活动、项目、月捐、日捐、义买活动、商品） |
| 18 | to_register_holder_guid | varchar | 32 |  |  | 转注册行为对象所属公募机构guid |
| 19 | to_register_institution_guid | varchar | 32 |  |  | 转注册行为对象所属执行机构guid |
| 20 | to_register_collect_name | varchar | 64 |  |  | 收藏的活动、项目、月捐、日捐、商品名称（只有行为为收藏时才统计） |

### 月捐人统计表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  | ID，自动递增 |
| 2 | username | varchar | 32 | 否 |  | 月捐人guid |
| 3 | wx_contract_id | varchar | 32 | 否 |  | 微信委托代扣协议ID |
| 4 | contract_id | varchar | 32 | 否 |  | 月捐ID |
| 5 | contract_name | varchar | 64 | 是 |  | 月捐名称 |
| 6 | fund_guid | varchar | 32 | 是 |  | 公募guid |
| 7 | fund_name | varchar | 64 | 是 |  | 公募名称 |
| 8 | ins_guid | varchar | 32 | 是 |  | 机构guid |
| 9 | ins_name | varchar | 64 | 是 |  | 机构名称 |
| 10 | field_guid | varchar | 64 | 是 |  | 捐助领域guid |
| 11 | people_guid | varchar | 64 | 是 |  | 受助对象guid |
| 12 | contract_time | datetime | 0 | 是 |  | 签约时间 |
| 13 | termination_time | datetime | 0 | 是 |  | 解约时间 |
| 14 | status | int | 1 | 是 |  | 签约状态（1：已签约；2：已解约） |
| 15 | amount | decimal | 15 | 是 |  | 当前签约金额 |
| 16 | contract_amount | decimal | 15 | 是 |  | 累计月捐金额 |
| 17 | contract_count | int | 11 | 是 |  | 累计月捐笔数 |
| 18 | create_time | datetime | 0 | 是 |  | 创建时间 |
| 19 | update_time | datetime | 0 | 是 |  | 更新时间 |

### 月捐解约行为统计表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 0 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | count_time | datetime | 0 | 否 |  | 统计时间（解约时间，YYYY-MM-DD 24h:mm:ss） |
| 3 | username | varchar | 32 | 否 |  | 月捐人guid |
| 4 | contract_id | varchar | 32 |  |  | 月捐ID |
| 5 | contract_time | datetime | 0 | 否 |  | 签约时间（YYYY-MM-DD 24h:mm:ss） |
| 6 | amount | double | 15 |  |  | 签约金额 |
| 7 | gear | int | 0 |  |  | 捐赠档位（1：0-10元；2：10-20元；3：20-30元；4：30-50元；5：50-100元；6：100-200元；7：200-500元。各档位都是“<=”） |
| 8 | termination_type | int | 0 |  |  | 解约方式（0～10：微信侧解约 11：个人中心解约 12：个人中心修改月捐 13：联劝网解约 14：联劝网修改月捐 15：机构解约 16：boss管理员解约 17：连续扣款失败，自动解约 ） |
| 9 | termination_remark | varchar | 512 |  |  | 解约原因 |
| 10 | termination_remark_format | varchar | 512 |  |  | 解约原因（格式一下，方便显示） |
| 11 | fund_guid | varchar | 32 |  |  | 公募基金会实体GUID |
| 12 | ins_guid | varchar | 32 |  |  | 机构实体GUID |
| 13 | extension | varchar | 16 |  |  | 当前线下劝募渠道 |
| 14 | source | varchar | 32 |  |  | 当前月捐邀请guid |
| 15 | remark | varchar | 1024 |  |  | 当前签约留言 |
| 16 | contract_code | varchar | 32 | 否 |  | 签约协议号 |
| 17 | create_time | datetime | 0 |  |  | 创建时间 |
| 18 | update_time | datetime | 0 |  |  | 更新时间 |

### 月捐邀请日统计表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 0 | 否 |  | ID，自动增长 |
| 2 | countDay | date | 0 |  |  | 统计日 |
| 3 | invitationId | bigint | 0 |  |  | 月捐邀请表id |
| 4 | initiateGuid | varchar | 64 |  |  | 月捐邀请发起者 |
| 5 | contractName | varchar | 64 |  |  | 月捐名称 |
| 6 | contractId | varchar | 64 |  |  | 月捐guid |
| 7 | insName | varchar | 64 |  |  | 发起机构名称 |
| 8 | insGuid | varchar | 64 |  |  | 发起机构guid |
| 9 | fundName | varchar | 64 |  |  | 认领机构名称 |
| 10 | fundGuid | varchar | 64 |  |  | 认领机构guid |
| 11 | allAmount | decimal | 15 |  |  | 累计总金额 |
| 12 | allCount | bigint | 0 |  |  | 累计签约人次 |
| 13 | addAmount | decimal | 15 |  |  | 当天新增金额 |
| 14 | addCount | bigint | 0 |  |  | 当日新增签约人次 |
| 15 | allRelieveCount | bigint | 0 |  |  | 累计解约人数 |
| 16 | addRelieveCount | bigint | 0 |  |  | 当天新增解约人数 |
| 17 | updateTime | datetime | 0 |  |  | 更新时间 |

### 每月捐赠统计表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  | ID，自动递增 |
| 2 | count_month | date | 0 | 是 |  | 统计月（YYYY-MM-01） |
| 3 | pool_type | int | 11 | 是 |  | 类型（3：活动；15：筹款产品；27：月捐；29：日捐） |
| 4 | pool_guid | varchar | 32 | 是 |  | 活动/项目/月捐/日捐guid |
| 5 | pool_name | varchar | 64 | 是 |  | 活动/项目/月捐/日捐名称 |
| 6 | ins_guid | varchar | 32 | 是 |  | 机构guid |
| 7 | ins_name | varchar | 64 | 是 |  | 机构名称 |
| 8 | fund_guid | varchar | 32 | 是 |  | 公募guid |
| 9 | fund_name | varchar | 64 | 是 |  | 公募名称 |
| 10 | order_type | int | 1 | 是 |  | 订单类型（0：报名费；1：捐款；5：抽签） |
| 11 | pay_type | int | 1 | 是 |  | 支付方式（1：支付宝；4：微信；8：移动和包；9：外部接口；99：配捐） |
| 12 | donate_type | int | 1 | 是 |  | 捐赠类型（0：普通捐赠；4：月捐；5：日捐） |
| 13 | all_amount | decimal | 15，2 | 是 |  | 金额 |
| 14 | all_count | int | 11 | 是 |  | 笔数 |
| 15 | create_time | datetime | 0 | 是 |  | 创建时间 |
| 16 | update_time | datetime | 0 | 是 |  | 更新时间 |


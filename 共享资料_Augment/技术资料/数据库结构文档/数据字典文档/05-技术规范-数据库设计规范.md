# 数据库设计规范

## 📋 规范概述

本规范基于上海联劝公益基金会现有数据库结构分析，总结了数据库设计的最佳实践和标准规范，为后续系统开发和维护提供指导。

## 🏗️ 数据库设计原则

### 基本原则

1. **业务导向**: 数据库设计必须紧密结合业务需求
2. **规范化设计**: 遵循数据库范式，避免数据冗余
3. **性能优化**: 在规范化和性能之间找到平衡点
4. **可扩展性**: 设计时考虑未来业务发展需要
5. **安全性**: 确保数据安全和隐私保护

### 设计理念

- **模块化**: 按业务模块划分数据库结构
- **标准化**: 统一的命名规范和数据类型
- **文档化**: 完整的设计文档和注释说明
- **版本控制**: 数据库结构变更的版本管理

## 📝 命名规范

### 表命名规范

1. **基本规则**:
   - 使用有意义的中文名称或英文名称
   - 避免使用系统保留字
   - 长度控制在64个字符以内

2. **命名模式**:
   ```
   业务模块表: 用户表、项目表、订单表
   关联表: 用户-项目关联表、订单-商品关联表
   系统表: t_系统前缀_表名 (如 t_xxbz_user)
   临时表: temp_表名
   日志表: log_表名
   ```

3. **推荐格式**:
   - 中文表名: 直接使用业务含义的中文名称
   - 英文表名: 使用小写字母，单词间用下划线分隔
   - 系统前缀: 使用系统标识作为前缀区分不同系统

### 字段命名规范

1. **主键字段**: 统一使用 `id`
2. **外键字段**: 使用 `关联表名Id` 格式
   ```
   userId (用户ID)
   projectId (项目ID)
   activityId (活动ID)
   institutionId (机构ID)
   ```

3. **时间字段**: 使用 `xxxTime` 格式
   ```
   createTime (创建时间)
   updateTime (更新时间)
   startTime (开始时间)
   endTime (结束时间)
   ```

4. **状态字段**: 统一使用 `status`
5. **类型字段**: 统一使用 `type`
6. **金额字段**: 统一使用 `amount`
7. **数量字段**: 统一使用 `count` 或 `quantity`

### 索引命名规范

```
主键索引: PK_表名
唯一索引: UK_表名_字段名
普通索引: IDX_表名_字段名
外键索引: FK_表名_字段名
```

## 🗃️ 数据类型规范

### 常用数据类型

| 用途 | 推荐类型 | 长度 | 说明 |
|------|----------|------|------|
| 主键ID | BIGINT | - | 自增长整数 |
| 外键ID | BIGINT | - | 关联表主键 |
| 状态码 | TINYINT | - | 0-255范围的状态值 |
| 金额 | DECIMAL | (15,2) | 精确的货币计算 |
| 百分比 | DECIMAL | (5,2) | 百分比数值 |
| 短文本 | VARCHAR | 50-255 | 名称、标题等 |
| 长文本 | TEXT | - | 描述、内容等 |
| 日期时间 | DATETIME | - | 精确到秒的时间 |
| 日期 | DATE | - | 仅日期部分 |
| 布尔值 | TINYINT | 1 | 0/1表示false/true |

### 字段长度建议

```
用户名: VARCHAR(50)
密码: VARCHAR(255) (加密后)
手机号: VARCHAR(20)
邮箱: VARCHAR(100)
身份证号: VARCHAR(20)
银行卡号: VARCHAR(30)
地址: VARCHAR(500)
备注: TEXT
```

## 🔑 约束和索引规范

### 主键约束

- 每个表必须有主键
- 推荐使用自增长整数作为主键
- 主键字段统一命名为 `id`

### 外键约束

- 外键字段必须建立索引
- 外键字段命名遵循 `关联表名Id` 格式
- 考虑性能影响，可选择性使用外键约束

### 唯一约束

```sql
-- 用户名唯一
ALTER TABLE 用户表 ADD CONSTRAINT UK_用户表_用户名 UNIQUE (用户名);

-- 手机号唯一
ALTER TABLE 用户表 ADD CONSTRAINT UK_用户表_手机号 UNIQUE (手机号);

-- 邮箱唯一
ALTER TABLE 用户表 ADD CONSTRAINT UK_用户表_邮箱 UNIQUE (邮箱);
```

### 索引策略

1. **必建索引**:
   - 主键索引（自动创建）
   - 外键字段索引
   - 频繁查询的字段索引

2. **选择性建索引**:
   - WHERE条件中的字段
   - ORDER BY排序字段
   - GROUP BY分组字段
   - JOIN连接字段

3. **复合索引**:
   - 多字段组合查询时建立复合索引
   - 注意字段顺序，选择性高的字段在前

## 📊 表结构设计规范

### 基础字段

每个业务表建议包含以下基础字段：

```sql
CREATE TABLE 示例表 (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    status TINYINT DEFAULT 1 COMMENT '状态(0:禁用,1:启用)',
    createTime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updateTime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    creater VARCHAR(50) COMMENT '创建者',
    updater VARCHAR(50) COMMENT '更新者',
    remark TEXT COMMENT '备注'
);
```

### 状态字段设计

```sql
-- 通用状态
status TINYINT DEFAULT 1 COMMENT '状态(0:禁用,1:启用,2:删除)'

-- 订单状态
orderStatus TINYINT COMMENT '订单状态(1:待支付,2:已支付,3:已完成,4:已取消)'

-- 审核状态
auditStatus TINYINT COMMENT '审核状态(0:待审核,1:审核通过,2:审核拒绝)'
```

### 软删除设计

```sql
-- 方式1: 使用status字段
status TINYINT DEFAULT 1 COMMENT '状态(0:禁用,1:启用,2:删除)'

-- 方式2: 使用专门的删除字段
isDeleted TINYINT DEFAULT 0 COMMENT '是否删除(0:否,1:是)'
deleteTime DATETIME COMMENT '删除时间'
```

## 🔒 安全设计规范

### 敏感信息处理

1. **密码存储**: 使用加密算法存储，不存储明文
2. **身份证号**: 部分加密或脱敏显示
3. **银行卡号**: 只显示后4位，其余用*代替
4. **手机号**: 中间4位用*代替

### 权限控制

```sql
-- 用户权限表
CREATE TABLE 用户权限表 (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    userId BIGINT NOT NULL COMMENT '用户ID',
    roleId BIGINT NOT NULL COMMENT '角色ID',
    createTime DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 角色权限表
CREATE TABLE 角色权限表 (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    roleId BIGINT NOT NULL COMMENT '角色ID',
    permissionId BIGINT NOT NULL COMMENT '权限ID',
    createTime DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 审计日志

```sql
-- 操作日志表
CREATE TABLE 操作日志表 (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    userId BIGINT COMMENT '操作用户ID',
    operation VARCHAR(100) COMMENT '操作类型',
    tableName VARCHAR(100) COMMENT '操作表名',
    recordId BIGINT COMMENT '操作记录ID',
    oldValue TEXT COMMENT '修改前数据',
    newValue TEXT COMMENT '修改后数据',
    createTime DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 📈 性能优化规范

### 查询优化

1. **避免SELECT ***:
   ```sql
   -- 不推荐
   SELECT * FROM 用户表;
   
   -- 推荐
   SELECT id, 用户名, 邮箱 FROM 用户表;
   ```

2. **使用LIMIT分页**:
   ```sql
   SELECT id, 用户名 FROM 用户表 
   ORDER BY createTime DESC 
   LIMIT 20 OFFSET 0;
   ```

3. **合理使用索引**:
   ```sql
   -- 利用索引的查询
   SELECT * FROM 用户表 WHERE userId = 123;
   
   -- 避免函数操作破坏索引
   SELECT * FROM 用户表 WHERE DATE(createTime) = '2025-01-01';
   ```

### 表分区策略

```sql
-- 按时间分区的日志表
CREATE TABLE 操作日志表 (
    id BIGINT AUTO_INCREMENT,
    userId BIGINT,
    operation VARCHAR(100),
    createTime DATETIME,
    PRIMARY KEY (id, createTime)
) PARTITION BY RANGE (YEAR(createTime)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026)
);
```

## 🔧 维护规范

### 版本控制

1. **结构变更记录**: 每次数据库结构变更都要记录
2. **脚本管理**: 使用版本化的SQL脚本管理变更
3. **回滚方案**: 每个变更都要准备回滚脚本

### 备份策略

1. **全量备份**: 每日进行全量备份
2. **增量备份**: 每小时进行增量备份
3. **异地备份**: 重要数据进行异地备份
4. **恢复测试**: 定期进行备份恢复测试

### 监控指标

1. **性能监控**: 慢查询、连接数、CPU使用率
2. **容量监控**: 磁盘空间、表大小、索引大小
3. **业务监控**: 关键业务指标的数据变化

---

*本规范基于上海联劝公益基金会现有数据库分析总结，为数据库设计和维护提供标准指导。*

# 捐赠管理模块 数据字典

## 模块概述

**业务模块**: 捐赠管理  
**关键词**: donation, donate, fund, 捐赠, 捐款, 基金  
**相关表数量**: 78  
**涉及系统**: 9个  

## 相关表清单

| 序号 | 表名 | 所属系统 | 字段数 | 说明 |
|------|------|----------|--------|------|
| 1 | 捐赠人表 | CRM客户关系管理系统 | 57 |  |
| 2 | 捐赠人重复表 | CRM客户关系管理系统 | 57 |  |
| 3 | 捐赠人临时表  | CRM客户关系管理系统 | 56 |  |
| 4 | 公益基金表 | 业务管理系统 | 52 |  |
| 5 | 捐赠人表 | 联劝CRM系统 | 35 |  |
| 6 | 线下捐赠表 | TAXUS系统 | 30 |  |
| 7 | 机构捐赠信息收集表 | HORN系统 | 28 |  |
| 8 | 捐赠用户信息收集表 | HORN系统 | 25 |  |
| 9 | 线下捐赠配捐表 | 财务支付系统 | 24 |  |
| 10 | 线下捐赠发票表 | TAXUS系统 | 23 |  |
| 11 | 我的捐赠故事作品表 | HORN系统 | 23 |  |
| 12 | 线下捐赠信息表 | 财务支付系统 | 22 |  |
| 13 | 每日捐赠统计表 | 统计分析系统 | 21 |  |
| 14 | 捐赠定制表 | HORN系统 | 18 |  |
| 15 | 物资捐赠同步信息表 | TAXUS系统 | 17 |  |
| 16 | 字节跳动票据申请捐赠明细信息表 | TAXUS系统 | 17 |  |
| 17 | 物资捐赠信息表 | HORN系统 | 17 |  |
| 18 | 每月捐赠统计表 | 统计分析系统 | 16 |  |
| 19 | t_egg_donation_enterprise | EGG系统 | 15 |  |
| 20 | 耐克志愿者时长捐赠表 | HORN系统 | 14 |  |
| 21 | t_egg_fund_project | EGG系统 | 13 |  |
| 22 | 订单捐赠对象变更记录 | 财务支付系统 | 12 |  |
| 23 | （新）公募基金支付设置表  | TAXUS系统 | 12 |  |
| 24 | 标签捐赠人关系表 | CRM客户关系管理系统 | 12 |  |
| 25 | 标签捐赠人关系表 | 联劝CRM系统 | 11 |  |
| 26 | 基金会接口开通情况表 | HORN系统 | 11 |  |
| 27 | 定期捐赠引流表 | HORN系统 | 11 |  |
| 28 | 定期捐赠引流表 | 财务支付系统 | 10 |  |
| 29 | 捐赠信息收集自定义配置表 | HORN系统 | 10 |  |
| 30 | 捐赠信息收集自定义配置修改表 | HORN系统 | 10 |  |
| 31 | 志愿者小时数捐赠表 | HORN系统 | 10 |  |
| 32 | 项目捐赠耐克配捐记录表 | 财务支付系统 | 9 |  |
| 33 | t_egg_fund_use | EGG系统 | 9 |  |
| 34 | 捐赠人用户轨迹表 | CRM客户关系管理系统 | 9 |  |
| 35 | 机构捐赠信息收集配置表 | HORN系统 | 9 |  |
| 36 | 机构捐赠收集信息配置修改表 | HORN系统 | 9 |  |
| 37 | 用户自定义捐赠信息表 | HORN系统 | 9 |  |
| 38 | 项目捐赠配捐统计表 | HORN系统 | 9 |  |
| 39 | 捐赠项配置表 | 财务支付系统 | 8 |  |
| 40 | 专项基金联劝官网设置表 | 业务管理系统 | 8 |  |
| 41 | 线下捐赠操作记录表 | 业务管理系统 | 8 |  |
| 42 | 联劝匿名实名月捐赠统计表 | TAXUS系统 | 8 |  |
| 43 | 联劝月捐赠支付渠道统计表 | TAXUS系统 | 8 |  |
| 44 | 个人月度捐款统计表 | TAXUS系统 | 8 |  |
| 45 | t_xxbz_funduse_detail | 小小包子系统 | 8 |  |
| 46 | 基金基金会机构月度收入统计表 | 财务支付系统 | 7 |  |
| 47 | 公益基金合作详情表 | 业务管理系统 | 7 |  |
| 48 | 每月捐赠统计 | 统计分析系统 | 7 |  |
| 49 | 捐赠人变更记录表 | TAXUS系统 | 7 |  |
| 50 | 基金年度账户表 | TAXUS系统 | 7 |  |
| 51 | 联劝月捐赠24小时时间段统计表 | TAXUS系统 | 7 |  |
| 52 | 捐赠人维护记录表 | CRM客户关系管理系统 | 7 |  |
| 53 | 捐赠统计表 | 财务支付系统 | 6 |  |
| 54 | 机构实名捐赠人数量统计表 | 财务支付系统 | 6 |  |
| 55 | 捐赠项配置表 | TAXUS系统 | 6 |  |
| 56 | 我的捐赠故事评分表 | HORN系统 | 6 |  |
| 57 | 我的捐赠故事海报表 | HORN系统 | 6 |  |
| 58 | t_xxbz_donationtype | 小小包子系统 | 6 |  |
| 59 | 个人年度捐款统计表 | 财务支付系统 | 5 |  |
| 60 | t_egg_donationtype | EGG系统 | 5 |  |
| 61 | t_egg_fund_file | EGG系统 | 5 |  |
| 62 | t_egg_team_FundAccount | EGG系统 | 5 |  |
| 63 | 捐赠统计表 | TAXUS系统 | 5 |  |
| 64 | 耐克志愿者时长捐赠明细表 | HORN系统 | 5 |  |
| 65 | t_xxbz_fund_use | 小小包子系统 | 5 |  |
| 66 | 捐赠人ID关联表 | TAXUS系统 | 4 |  |
| 67 | 物资捐赠图片表 | HORN系统 | 4 |  |
| 68 | 分组捐赠人关系表 | 联劝CRM系统 | 3 |  |
| 69 | t_egg_fund_region | EGG系统 | 3 |  |
| 70 | 线下捐赠审核权限表 | TAXUS系统 | 3 |  |
| 71 | 分组捐赠人关系表 | CRM客户关系管理系统 | 3 |  |
| 72 | 对账未成功异常订单捐赠时间统计表 | 财务支付系统 | 2 |  |
| 73 | 捐赠人用户ID | CRM客户关系管理系统 | 2 |  |
| 74 | temp_fund_coo_field | 业务管理系统 | 1 |  |
| 75 | 捐赠配置定义表 | 业务管理系统 | 0 |  |
| 76 | 捐赠信息收集表 | 业务管理系统 | 0 |  |
| 77 | 公募基金表 | 业务管理系统 | 0 |  |
| 78 | t_egg_fund_field | EGG系统 | 0 |  |

## 主要表结构详情

### 捐赠人表 (CRM客户关系管理系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号,自动增长 |
| 2 | userName | varchar | 32 | 否 |  | 第三方用户名 |
| 3 | userSource | int | 2 | 否 |  | 用户来源(1:联劝网 2：微信 3：用户导入 4：用户录入 5：用户合并 6：问卷调查 ) |
| 4 | userGuid | bigint | 32 | 否 |  | 用户名（唯一标示，年+月+日+时+分+秒+4位userID） |
| 5 | name | varchar | 64 |  |  | 真实姓名 |
| 6 | namePinyin | varchar | 64 |  |  | 姓名拼音 |
| 7 | nationality | int | 1 |  |  | 国籍（0：中国大陆；1：港澳及外籍人士；2：台湾同胞） |
| 8 | identitycard | varchar | 32 |  |  | 证件号（0：身份证号；1：护照；2：台胞证） |
| 9 | sex | int | 1 |  |  | 性别(0:未知;1:男;2:女) |
| 10 | birthday | Date | 0 |  |  | 出生日期（YYYY/MM/DD） |
| 11 | constellation | int | 2 |  |  | 星座（1至12，具体参见如下） |
| 12 | zodiac | int | 2 |  |  | 生肖（1至12，具体参见如下） |
| 13 | degree | int | 1 |  |  | 教育程度（0：未知；1：小学；2：初中；3：高中/中专；4大专：；5：本科；6：硕士；7：博士；8：博士后；9：文盲） |
| 14 | job | int | 1 |  |  | 职业信息（0：学生；1：公司职员；2：国企事业单位职员；3：自由职业者；4：公益从业人员；5：政府机关人员；6：媒体；7：其他） |
| 15 | ismarry | int | 1 |  |  | 婚姻状况(0:未知; 1:已婚; 2:未婚; 3:离异; 4:丧偶) |
| 16 | province | varchar | 32 |  |  | 现住址地址的省或直辖市的地名 |
| 17 | city | varchar | 32 |  |  | 现住址地址的城市 |
| 18 | area | varchar | 32 |  |  | 现住址地址的区 |
| 19 | fullAddress | varchar | 255 |  |  | 现住址具体地址 |
| 20 | telphone | varchar | 32 |  |  | 住宅电话 |
| 21 | phone | varchar | 32 |  |  | 移动手机 |
| 22 | mail | varchar | 64 |  |  | 邮箱 |
| 23 | qq | varchar | 64 |  |  | QQ号（个人信息） |
| 24 | qqCode | varchar | 16 |  |  | QQ（openid，认证登录用） |
| 25 | weibo | varchar | 64 |  |  | 微博（个人信息） |
| 26 | weiboCode | varchar | 48 |  |  | 微博（openid，认证登录用） |
| 27 | weixin | varchar | 64 |  |  | 微信（个人信息） |
| 28 | weixinCode | varchar | 32 |  |  | 微信（openid，认证登录用） |
| 29 | unionid | varchar | 32 |  |  | 微信用UNIONID |
| 30 | douyin | varchar | 64 |  |  | 抖音号 |
| 31 | linkedin | varchar | 64 |  |  | 领英号 |
| 32 | hukouProvince | varchar | 32 |  |  | 户籍地址的省或直辖市的地名 |
| 33 | hukouCity | varchar | 32 |  |  | 户籍址地址的城市 |
| 34 | hukouArea | varchar | 32 |  |  | 户籍址地址的区 |
| 35 | isCityWide | int | 1 |  |  | 现住址和户籍是同一个城市(0:未知; 1:不是; 2:是) |
| 36 | postCode | varchar | 10 |  |  | 邮政编码 |
| 37 | createTime | DateTime | 0 | 否 |  | 创建时间 |
| 38 | creator | varchar | 32 |  |  | 创建者用户名（系统账户时为空，其余不为空） |
| 39 | creatorAccount | varchar | 32 | 否 |  | 创建者账户名 |
| 40 | updateTime | DateTime | 0 |  |  | 更新时间 |
| 41 | updater | varchar | 32 |  |  | 更新者用户名（系统账户时为空，其余不为空） |
| 42 | updaterAccount | varchar | 32 |  |  | 更新者账户名 |
| 43 | fax | varchar | 32 |  |  | 传真 |
| 44 | company | varchar | 32 |  |  | 单位 |
| 45 | companyPost | varchar | 32 |  |  | 单位职务 |
| 46 | companyPhone | varchar | 32 |  |  | 单位电话 |
| 47 | taobaoAccount | varchar | 32 |  |  | 淘宝账号 |
| 48 | alipayAccount | varchar | 32 |  |  | 支付宝账号 |
| 49 | companyPhone | varchar | 32 |  |  | 单位部门 |
| 50 | nickName | varchar | 32 |  |  | 昵称 |
| 51 | remarks | varchar | 32 |  |  | 备注 |
| 52 | institutionId | int | 11 |  |  | 机构Id |
| 53 | flg | int | 1 |  |  | 回滚标志（0：不能回滚；1：能回滚） |
| 54 | userSourceCode | varchar | 50 |  |  | 公众号原始id |
| 55 | headimgurl | varchar | 200 |  |  | 用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），用户没有头像时该项为空。若用户更换头像，原有头像URL将失效。 |
| 56 | ip | varchar | 64 |  |  | ip地址 |
| 57 | shareAccount | varchar | 1024 |  |  | 数据共享给子账户的名单 |

### 捐赠人重复表 (CRM客户关系管理系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号,自动增长 |
| 2 | userName | varchar | 32 | 否 |  | 第三方用户名 |
| 3 | userSource | int | 2 | 否 |  | 用户来源(1:联劝网 2：微信 3：用户导入 4：用户录入 5：用户合并 ) |
| 4 | userGuid | bigint | 32 | 否 |  | 用户名（唯一标示，年+月+日+时+分+秒+4位userID） |
| 5 | name | varchar | 64 |  |  | 真实姓名 |
| 6 | namePinyin | varchar | 64 |  |  | 姓名拼音 |
| 7 | nationality | int | 1 |  |  | 国籍（0：中国大陆；1：港澳及外籍人士；2：台湾同胞） |
| 8 | identitycard | varchar | 32 |  |  | 证件号（0：身份证号；1：护照；2：台胞证） |
| 9 | sex | int | 1 |  |  | 性别(0:未知;1:男;2:女) |
| 10 | birthday | Date | 0 |  |  | 出生日期（YYYY/MM/DD） |
| 11 | constellation | int | 2 |  |  | 星座（1至12，具体参见如下） |
| 12 | zodiac | int | 2 |  |  | 生肖（1至12，具体参见如下） |
| 13 | degree | int | 1 |  |  | 教育程度（0：未知；1：小学；2：初中；3：高中/中专；4大专：；5：本科；6：硕士；7：博士；8：博士后；9：文盲） |
| 14 | job | int | 1 |  |  | 职业信息（0：学生；1：公司职员；2：国企事业单位职员；3：自由职业者；4：公益从业人员；5：政府机关人员；6：媒体；7：其他） |
| 15 | ismarry | int | 1 |  |  | 婚姻状况(0:未知; 1:已婚; 2:未婚; 3:离异; 4:丧偶) |
| 16 | province | varchar | 32 |  |  | 现住址地址的省或直辖市的地名 |
| 17 | city | varchar | 32 |  |  | 现住址地址的城市 |
| 18 | area | varchar | 32 |  |  | 现住址地址的区 |
| 19 | fullAddress | varchar | 255 |  |  | 现住址具体地址 |
| 20 | telphone | varchar | 32 |  |  | 住宅电话 |
| 21 | phone | varchar | 32 |  |  | 移动手机 |
| 22 | mail | varchar | 64 |  |  | 邮箱 |
| 23 | qq | varchar | 64 |  |  | QQ号（个人信息） |
| 24 | qqCode | varchar | 16 |  |  | QQ（openid，认证登录用） |
| 25 | weibo | varchar | 64 |  |  | 微博（个人信息） |
| 26 | weiboCode | varchar | 48 |  |  | 微博（openid，认证登录用） |
| 27 | weixin | varchar | 64 |  |  | 微信（个人信息） |
| 28 | weixinCode | varchar | 32 |  |  | 微信（openid，认证登录用） |
| 29 | unionid | varchar | 32 |  |  | 微信用UNIONID |
| 30 | hukouProvince | varchar | 32 |  |  | 户籍地址的省或直辖市的地名 |
| 31 | hukouCity | varchar | 32 |  |  | 户籍址地址的城市 |
| 32 | hukouArea | varchar | 32 |  |  | 户籍址地址的区 |
| 33 | isCityWide | int | 1 |  |  | 现住址和户籍是同一个城市(0:未知; 1:不是; 2:是) |
| 34 | postCode | varchar | 10 |  |  | 邮政编码 |
| 35 | createTime | DateTime | 0 | 否 |  | 创建时间 |
| 36 | creator | varchar | 32 |  |  | 创建者用户名（系统账户时为空，其余不为空） |
| 37 | creatorAccount | varchar | 32 | 否 |  | 创建者账户名 |
| 38 | updateTime | DateTime | 0 |  |  | 更新时间 |
| 39 | updater | varchar | 32 |  |  | 更新者用户名（系统账户时为空，其余不为空） |
| 40 | updaterAccount | varchar | 32 |  |  | 更新者账户名 |
| 41 | fax | varchar | 32 |  |  | 传真 |
| 42 | company | varchar | 32 |  |  | 单位 |
| 43 | companyPost | varchar | 32 |  |  | 单位职务 |
| 44 | companyPhone | varchar | 32 |  |  | 单位电话 |
| 45 | taobaoAccount | varchar | 32 |  |  | 淘宝账号 |
| 46 | alipayAccount | varchar | 32 |  |  | 支付宝账号 |
| 47 | companyPhone | varchar | 32 |  |  | 单位部门 |
| 48 | nickName | varchar | 32 |  |  | 昵称 |
| 49 | remarks | varchar | 32 |  |  | 备注 |
| 50 | institutionId | int | 11 |  |  | 机构Id |
| 51 | flg | int | 1 |  |  | 回滚标志（0：不能回滚；1：能回滚） |
| 52 | userSourceCode | varchar | 50 |  |  | 公众号原始id |
| 53 | headimgurl | varchar | 200 |  |  | 用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），用户没有头像时该项为空。若用户更换头像，原有头像URL将失效。 |
| 54 | douyin | varchar | 64 |  |  | 抖音号 |
| 55 | linkedin | varchar | 64 |  |  | 领英号 |
| 56 | ip | varchar | 64 |  |  | ip地址 |
| 57 | newUserGuid | bigint | 32 | 否 |  | 合并后新的Guid |

### 捐赠人临时表  (CRM客户关系管理系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号,自动增长 |
| 2 | userName | varchar | 32 | 否 |  | 第三方用户名 |
| 3 | userGuid | bigint | 32 | 否 |  | 用户名（唯一标示，M+年+月+日+时+分+秒+） |
| 4 | name | varchar | 64 |  |  | 真实姓名 |
| 5 | namePinyin | varchar | 64 |  |  | 姓名拼音 |
| 6 | nationality | int | 1 |  |  | 国籍（0：中国大陆；1：港澳及外籍人士；2：台湾同胞） |
| 7 | identitycard | varchar | 32 |  |  | 证件号（0：身份证号；1：护照；2：台胞证） |
| 8 | sex | int | 1 |  |  | 性别(0:未知;1:男;2:女) |
| 9 | birthday | Date | 0 |  |  | 出生日期（YYYY/MM/DD） |
| 10 | constellation | int | 2 |  |  | 星座（1至12，具体参见如下） |
| 11 | zodiac | int | 2 |  |  | 生肖（1至12，具体参见如下） |
| 12 | degree | int | 1 |  |  | 教育程度（0：未知；1：小学；2：初中；3：高中/中专；4大专：；5：本科；6：本科以上；） |
| 13 | job | int | 1 |  |  | 职业信息（0：学生；1：公司职员；2：国企事业单位职员；3：自由职业者；4：公益从业人员；5：政府机关人员；6：媒体；7：其他） |
| 14 | ismarry | int | 1 |  |  | 婚姻状况(0:未知; 1:已婚; 2:未婚; 3:离异; 4:丧偶) |
| 15 | province | varchar | 32 |  |  | 现住址地址的省或直辖市的地名 |
| 16 | city | varchar | 32 |  |  | 现住址地址的城市 |
| 17 | area | varchar | 32 |  |  | 现住址地址的区 |
| 18 | fullAddress | varchar | 255 |  |  | 现住址具体地址 |
| 19 | telphone | varchar | 32 |  |  | 住宅电话 |
| 20 | phone | varchar | 32 |  |  | 移动手机 |
| 21 | mail | varchar | 64 |  |  | 邮箱 |
| 22 | qq | varchar | 64 |  |  | QQ（openid，认证登录用） |
| 23 | qqCode | varchar | 16 |  |  | QQ号（个人信息） |
| 24 | weibo | varchar | 64 |  |  | 微博（openid，认证登录用） |
| 25 | weiboCode | varchar | 48 |  |  | 微博（个人信息） |
| 26 | weixin | varchar | 64 |  |  | 微信（openid，认证登录用） |
| 27 | weixinCode | varchar | 32 |  |  | 微信（个人信息） |
| 28 | unionid | varchar | 32 |  |  | 微信用UNIONID |
| 29 | hukouProvince | varchar | 32 |  |  | 户籍地址的省或直辖市的地名 |
| 30 | hukouCity | varchar | 32 |  |  | 户籍址地址的城市 |
| 31 | hukouArea | varchar | 32 |  |  | 户籍址地址的区 |
| 32 | isCityWide | int | 1 |  |  | 现住址和户籍是同一个城市(0:未知; 1:不是; 2:是) |
| 33 | postCode | varchar | 10 |  |  | 邮政编码 |
| 34 | createTime | DateTime | 0 | 否 |  | 创建时间 |
| 35 | creator | varchar | 32 |  |  | 创建者用户名（系统账户时为空，其余不为空） |
| 36 | creatorAccount | varchar | 32 | 否 |  | 创建者账户名 |
| 37 | updateTime | DateTime | 0 |  |  | 更新时间 |
| 38 | updater | varchar | 32 |  |  | 更新者用户名（系统账户时为空，其余不为空） |
| 39 | updaterAccount | varchar | 32 |  |  | 更新者账户名 |
| 40 | fax | varchar | 32 |  |  | 传真 |
| 41 | company | varchar | 32 |  |  | 单位 |
| 42 | companyPost | varchar | 32 |  |  | 单位职务 |
| 43 | companyPhone | varchar | 32 |  |  | 单位电话 |
| 44 | taobaoAccount | varchar | 32 |  |  | 淘宝账号 |
| 45 | alipayAccount | varchar | 32 |  |  | 支付宝账号 |
| 46 | companyPhone | varchar | 32 |  |  | 单位部门 |
| 47 | nickName | varchar | 32 |  |  | 昵称 |
| 48 | remarks | varchar | 32 |  |  | 备注 |
| 49 | institutionId | int | 11 |  |  | 机构Id |
| 50 | flg | int | 1 |  |  | 标志 |
| 51 | successFlag | int | 1 |  |  | 导入是否成功（0：成功；1：失败；2重复） |
| 52 | errorMsg | varchar | 255 |  |  | 错误原因 |
| 53 | excelNum | int | 4 |  |  | 每行数据所在excel表的列 |
| 54 | douyin | varchar | 64 |  |  | 抖音号 |
| 55 | linkedin | varchar | 64 |  |  | 领英号 |
| 56 | ip | varchar | 64 |  |  | ip地址 |

### 公益基金表 (业务管理系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | fundId | varchar | 32 | 否 |  | 基金ID（FUND开头的） |
| 3 | fundName | varchar | 32 | 否 |  | 基金名称 |
| 4 | fundAbbreviation | varchar | 12 |  |  | 基金简称 |
| 5 | fundLogo | int | 11 |  |  | 主题LOGO，列表页面（主题图片比例为8：9） |
| 6 | fundBigPicId | int | 11 |  |  | 主题图片ID，详情页面（主题图片比例为4:3） |
| 7 | fundIntroduction | text |  |  |  | 基金简介 |
| 8 | fundDetail | text |  |  |  | 基金详情 |
| 9 | fundUse | text |  |  |  | 资金使用 |
| 10 | fundTarget1 | decimal | 15,2 |  | 10 | 筹款标的1 |
| 11 | fundTarget2 | decimal | 15,2 |  | 20 | 筹款标的2 |
| 12 | fundTarget3 | decimal | 15,2 |  | 50 | 筹款标的3 |
| 13 | keywords1 | varchar | 5 |  |  | 关键字1 |
| 14 | keywords2 | varchar | 5 |  |  | 关键字2 |
| 15 | keywords3 | varchar | 5 |  |  | 关键字3 |
| 16 | fundraisingMaxAmount | decimal | 15,2 |  |  | 筹款限额 |
| 17 | fundStyle | int | 1 |  |  | 基金领域 |
| 18 | status | int | 1 | 否 | 0 | 基金状态（0：未发布；1：已发布；2：已撤消；3：审核不通过） |
| 19 | institutionId | int | 11 |  |  | 机构ID |
| 20 | institutionIntroduction | text |  |  |  | 机构简介 |
| 21 | institutionShowFlag | int | 1 |  | 0 | 是否显示机构（0：不显示；1：显示） |
| 22 | accountAll | varchar | 32 |  |  | 基金账户（总账户） |
| 23 | accountOnLine | varchar | 32 |  |  | 联劝网平台账户(线上账户) |
| 24 | accountOffLine | varchar | 32 |  |  | 第三方平台账户（线下账户） |
| 25 | createTime | datetime |  |  |  | 创建时间 |
| 26 | applyTime | datetime |  |  |  | 发布时间 |
| 27 | applyer | varchar | 32 |  |  | 发布者（用户名） |
| 28 | applyNickName | varchar | 32 |  |  | 发布者昵称 |
| 29 | cancelReason | varchar | 200 |  |  | 撤消理由 |
| 30 | cancelTime | datetime |  |  |  | 撤消时间 |
| 31 | anonymousAccount | varchar | 32 |  |  | 匿名捐赠账户 |
| 32 | adminFeeType | tinyint | 1 |  |  | 行政管理费提取类型（0：不提取 1：固定比例提取 2：累进金额按约定比例提取 ） |
| 33 | adminFeeFixedRate | decimal | 5,2 |  |  | 行政管理费固定提取比例(非固定比例为空) |
| 34 | keywordsId1 | int | 11 |  |  | 关键字Id1 |
| 35 | keywordsId2 | int | 11 |  |  | 关键字Id2 |
| 36 | keywordsId3 | int | 11 |  |  | 关键字Id3 |
| 37 | showFlag | int | 1 | 否 | 0 | 联劝网是否显示（0：不显示；1：显示） |
| 38 | indexShowOrder | int | 11 |  |  | 首页显示顺 |
| 39 | type | int | 11 |  | 1 | 基金类别（1：公益基金；2：企业基金；3：联劝自发（联劝统筹）；4：联劝网基金（没有数据）；5：捐赠人建议基金）（标签系统用 99：公募基金，公募基金信息记录在公募基金表） |
| 40 | ifNeedPhoneAM | int | 1 | 否 | 0 | 匿名捐赠是否必填手机（0：非必填；1：必填） |
| 41 | ifNeedEmailAM | int | 1 | 否 | 0 | 匿名捐赠是否必填邮箱（0：非必填；1：必填） |
| 42 | needContract | int | 1 | 否 | 0 | 是否开通月捐（0：不开通；1：开通；2：审核中；3：审核不通过；4：审核通过；5：停止） |
| 43 | accountContract | varchar | 32 |  |  | 月捐账户 |
| 44 | wxPlanId | varchar | 28 |  |  | 微信月捐模板ID |
| 45 | ifNeedFund | int | 1 | 否 | 0 | 是否需要筹款（0：要筹款；1：不要筹款） |
| 46 | fundLogoedit | int | 11 |  |  | 审核前备份图片ID |
| 47 | fundBigPicIdedit | int | 11 |  |  | 审核前备份图片ID |
| 48 | recordNumber | varchar | 255 |  |  | 备案编号 |
| 49 | isSingleDonate | int | 2 | 否 | 1 | 是否需要单笔捐（1：需要；2：不需要） |
| 50 | planType | int | 1 |  |  | 联劝统筹类型（0：统筹资助；1：统筹合作） |
| 51 | buildTime | date | 0 |  |  | 基金设立日期 |
| 52 | limitTime | date | 0 |  |  | 认证截止有效日期（为空表示长期） |

### 捐赠人表 (联劝CRM系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  |  |
| 2 | user_guid | varchar | 64 |  |  | 用户GUID |
| 3 | lqw_username | varchar | 32 |  |  | 联劝网用户名 |
| 4 | lqw_registration_time | datetime |  |  |  | 联劝网注册时间 |
| 5 | user_source | int | 11 |  |  | 用户来源(1:联劝网 2：用户添加) |
| 6 | nick_name | varchar | 32 |  |  | 匹配字段-昵称 |
| 7 | name | varchar | 64 |  |  | 匹配字段-姓名 |
| 8 | name_pinyin | varchar | 64 |  |  | 姓名拼音 |
| 9 | nationality | int | 11 |  |  | 匹配字段-国籍 |
| 10 | identitycard | varchar | 32 |  |  | 匹配字段-证件号 |
| 11 | gender | varchar | 2 |  |  | 匹配字段-性别 |
| 12 | birthday | date |  |  |  | 匹配字段-出生日期 |
| 13 | phone | varchar | 32 |  |  | 匹配字段-手机号 |
| 14 | donate_amount | decimal | 15,2 |  |  | 捐赠金额 |
| 15 | donate_count | int | 11 |  |  | 捐赠次数 |
| 16 | fundraise_amount | decimal | 15,2 |  |  | 筹款金额 |
| 17 | fundraise_count | int | 11 |  |  | 筹款次数 |
| 18 | sms_unsubscribe | tinyint | 4 |  | 0 | 短信退订(0:未退订 1：已退订) |
| 19 | mail | varchar | 64 |  |  | 匹配字段-邮箱 |
| 20 | mail_unsubscribe | tinyint | 4 |  | 0 | 邮件退订(0:未退订 1：已退订) |
| 21 | region | varchar | 255 |  |  | 匹配字段-地区 |
| 22 | wx_flag | tinyint | 4 |  | 0 | 是否关注微信（0：否 1：是） |
| 23 | wx_name | varchar | 1024 |  |  | 微信名称 |
| 24 | wx_unionid | varchar | 32 |  |  | 微信unionid |
| 25 | wx_openid | varchar | 40 |  |  | 微信用户的唯一标识 |
| 26 | mail_address | varchar | 255 |  |  | 邮件地址 |
| 27 | monthly_donation | tinyint | 4 |  |  | 月捐状态（0：否 1：是） |
| 28 | monthly_donation_no | varchar | 32 |  |  | 月捐编号 |
| 29 | first_donation_time | datetime |  |  |  | 首次捐赠时间 |
| 30 | last_donation_time | datetime |  |  |  | 最近捐赠时间 |
| 31 | import_guid | varchar | 40 |  |  | 导入批次 |
| 32 | create_time | datetime |  |  |  |  |
| 33 | creator | varchar | 32 |  |  |  |
| 34 | update_time | datetime |  |  |  |  |
| 35 | updater | varchar | 32 |  |  |  |


# HORN系统 数据字典

## 系统概述

**系统名称**: HORN系统  
**系统标识**: horn  
**文件来源**: horn_dataBaseModelDesign.xls  
**工作表总数**: 285  
**有效表数量**: 280  
**字段总数**: 3805  

## 表结构清单

| 序号 | 表名 | 字段数 | 说明 |
|------|------|--------|------|
| 1 | 商户信息表 | 91 |  |
| 2 | 公益活动表 | 87 |  |
| 3 | 月捐表 | 86 |  |
| 4 | 公益项目表  | 80 |  |
| 5 | 日捐表 | 74 |  |
| 6 | 公益活动编辑表 | 72 |  |
| 7 | 月捐编辑表 | 67 |  |
| 8 | 公益项目修改表 | 66 |  |
| 9 | 日捐编辑表 | 63 |  |
| 10 | 用户表 | 51 |  |
| 11 | 队伍参赛类型表 | 39 |  |
| 12 | 联合捐表 | 39 |  |
| 13 | 耐克志愿活动信息表 | 34 |  |
| 14 | 耐克志愿活动信息修改表  | 34 |  |
| 15 | 公益系列表 | 31 |  |
| 16 | 报名活动配置信息修改表 | 30 |  |
| 17 | 报名活动配置信息表 | 30 |  |
| 18 | 志愿者活动表 | 30 |  |
| 19 | 志愿者活动编辑表 | 30 |  |
| 20 | 报名队列表 | 29 |  |
| 21 | 月捐开票信息表 | 29 |  |
| 22 | 机构捐赠信息收集表 | 28 |  |
| 23 | 月捐短信模板表 | 28 |  |
| 24 | 志愿者小时数登记表 | 28 |  |
| 25 | 支付信息表 | 26 |  |
| 26 | 页面信息配置表 | 26 |  |
| 27 | 邮件发送队列表  | 26 |  |
| 28 | 活动-团体-队伍表 | 25 |  |
| 29 | 捐赠用户信息收集表 | 25 |  |
| 30 | 机构账户关联表 | 24 |  |
| 31 | 拓展属性表 | 23 |  |
| 32 | 备案号提醒信息表 | 23 |  |
| 33 | 我的捐赠故事作品表 | 23 |  |
| 34 | 系列活动项目月捐表 | 22 |  |
| 35 | 用户个人报名表 | 22 |  |
| 36 | 分活动编辑表 | 22 |  |
| 37 | 分活动表 | 22 |  |
| 38 | 留言评价表 | 21 |  |
| 39 | 模板表  | 21 |  |
| 40 | 募款资料表 | 21 |  |
| 41 | 团体表 | 21 |  |
| 42 | 邀请捐表 | 21 |  |
| 43 | 反馈提醒信息表 | 21 |  |
| 44 | 问卷表 | 21 |  |
| 45 | 公募信息募捐方案表 | 20 |  |
| 46 | 短信发送队列表 | 20 |  |
| 47 | 黑名单表  | 20 |  |
| 48 | 证书模板表 | 20 |  |
| 49 | 临时模板表 | 20 |  |
| 50 | 账单下载记录表 | 20 |  |
| 51 | 益盒最佳项目表 | 20 |  |
| 52 | 邮件任务表 | 19 |  |
| 53 | 资质证明监测表 | 19 |  |
| 54 | 菜单表 | 18 |  |
| 55 | 反馈信息表 | 18 |  |
| 56 | 义买商品表 | 18 |  |
| 57 | 商品主订单表 | 18 |  |
| 58 | zither菜单表 | 18 |  |
| 59 | 新闻动态表 | 18 |  |
| 60 | 捐赠定制表 | 18 |  |
| 61 | 义卖商品预订单表 | 17 |  |
| 62 | 匿名开票临时表 | 17 |  |
| 63 | 账单下载配置表 | 17 |  |
| 64 | 物资捐赠信息表 | 17 |  |
| 65 | 业务消息通知表 | 16 |  |
| 66 | 拖拽ui表 | 16 |  |
| 67 | 公募信息表 | 16 |  |
| 68 | 定制专题页项目关联表 | 16 |  |
| 69 | 操作日志表 | 15 |  |
| 70 | horn图片表 | 15 |  |
| 71 | 自定义月捐短信模板表 | 15 |  |
| 72 | 月捐短信模板审核表 | 15 |  |
| 73 | 认领对象信息表 | 15 |  |
| 74 | 外部订单表 | 15 |  |
| 75 | 外部项目下载图片表 | 15 |  |
| 76 | 备案号通知记录表 | 15 |  |
| 77 | 反馈提醒规则表 | 15 |  |
| 78 | 海报模板表 | 15 |  |
| 79 | 评分记录表 | 14 |  |
| 80 | 角色表 | 14 |  |
| 81 | 商品物流表 | 14 |  |
| 82 | 活动-用户表 | 14 |  |
| 83 | 模板文字属性表 | 14 |  |
| 84 | boss用户表 | 14 |  |
| 85 | 拖拽模块模板表 | 14 |  |
| 86 | 反馈提醒记录表 | 14 |  |
| 87 | 短链接表 | 14 |  |
| 88 | 月捐邀请模板表 | 14 |  |
| 89 | 问卷模块表 | 14 |  |
| 90 | 海报模板文字属性表 | 14 |  |
| 91 | 机构证书到期邮件记录表 | 14 |  |
| 92 | 耐克志愿者时长捐赠表 | 14 |  |
| 93 | 错误日志表 | 13 |  |
| 94 | 短信发送记录表 | 13 |  |
| 95 | 模块表 | 13 |  |
| 96 | 商品从订单表 | 13 |  |
| 97 | scrm标签表 | 13 |  |
| 98 | 用户表单信息表 | 13 |  |
| 99 | 企业配捐表 | 13 |  |
| 100 | 备案号提醒规则表  | 13 |  |
| 101 | 开放式支付配置表 | 13 |  |
| 102 | 新虹记“疫”表 | 13 |  |
| 103 | 问卷答案表 | 13 |  |
| 104 | 耐克在线项目推荐表 | 13 |  |
| 105 | 逾期惩罚记录表 | 13 |  |
| 106 | 登录日志表 | 12 |  |
| 107 | 模块图片表 | 12 |  |
| 108 | 爱铺满模板配置表 | 12 |  |
| 109 | 外部用户表 | 12 |  |
| 110 | 资源中心表 | 12 |  |
| 111 | 资源推荐表 | 12 |  |
| 112 | 资质证明监测规则表 | 12 |  |
| 113 | 邮件验证信息表 | 11 |  |
| 114 | 用户-队伍表 | 11 |  |
| 115 | 报名费主订单表 | 11 |  |
| 116 | 机构年统计表 | 11 |  |
| 117 | 基金会接口开通情况表 | 11 |  |
| 118 | 定期捐赠引流表 | 11 |  |
| 119 | 资源渠道表 | 11 |  |
| 120 | 资源访问日志表 | 11 |  |
| 121 | 义卖活动电子劵记录表 | 11 |  |
| 122 | 用户志愿者小时数累计表 | 11 |  |
| 123 | 志愿小时数统计表 | 11 |  |
| 124 | 第三方用户关联表 | 11 |  |
| 125 | 筹款产品短链接表 | 11 |  |
| 126 | 用户菜单关系表 | 10 |  |
| 127 | 用户信息收集表 | 10 |  |
| 128 | 订单表 | 10 |  |
| 129 | 活动项目商品关系表 | 10 |  |
| 130 | 活动项目商品关系编辑表 | 10 |  |
| 131 | 地域表 | 10 |  |
| 132 | 证书模板审核表 | 10 |  |
| 133 | 邀请捐用户表 | 10 |  |
| 134 | 拓展属性记录表 | 10 |  |
| 135 | 捐赠信息收集自定义配置表 | 10 |  |
| 136 | 捐赠信息收集自定义配置修改表 | 10 |  |
| 137 | 专题页项目配捐比例调整表 | 10 |  |
| 138 | 志愿者小时数捐赠表 | 10 |  |
| 139 | 问卷主体信息表 | 10 |  |
| 140 | 耐克员工用户表 | 10 |  |
| 141 | 耐克企业微信通知记录表 | 10 |  |
| 142 | 显示变更记录表 | 10 |  |
| 143 | 导出记录表 | 10 |  |
| 144 | 监测规则表 | 10 |  |
| 145 | 邮箱验证表 | 9 |  |
| 146 | 投诉建议表 | 9 |  |
| 147 | 机构捐赠信息收集配置表 | 9 |  |
| 148 | 机构捐赠收集信息配置修改表 | 9 |  |
| 149 | 月捐用户展示图表 | 9 |  |
| 150 | 日捐用户展示图表 | 9 |  |
| 151 | 义买自定义设置编辑表 | 9 |  |
| 152 | 义买自定义设置表 | 9 |  |
| 153 | 活动邀请码表 | 9 |  |
| 154 | 报名费从订单表 | 9 |  |
| 155 | 爱扑满审核表 | 9 |  |
| 156 | 回调状态表 | 9 |  |
| 157 | 银行表 | 9 |  |
| 158 | 外部项目图片状态表 | 9 |  |
| 159 | 站内信表 | 9 |  |
| 160 | 常见问题表 | 9 |  |
| 161 | 审核记录表 | 9 |  |
| 162 | 邮件任务联络表 | 9 |  |
| 163 | 用户自定义捐赠信息表 | 9 |  |
| 164 | 项目捐赠配捐统计表 | 9 |  |
| 165 | 益盒最佳项目选择表 | 9 |  |
| 166 | 大家都在关注表 | 9 |  |
| 167 | 业务消息通知详细表 | 8 |  |
| 168 | 留言回复表 | 8 |  |
| 169 | 角色菜单关系表 | 8 |  |
| 170 | 用户角色关系表 | 8 |  |
| 171 | 系列99腾讯公益设置表 | 8 |  |
| 172 | 图片表 | 8 |  |
| 173 | 用户个人展示信息表 | 8 |  |
| 174 | 队伍表 | 8 |  |
| 175 | 证书表 | 8 |  |
| 176 | 升级公告表 | 8 |  |
| 177 | 新闻动态图片附件表 | 8 |  |
| 178 | 推荐项访问日志表  | 8 |  |
| 179 | 推广文案表 | 8 |  |
| 180 | 电子劵义卖活动配置表 | 8 |  |
| 181 | 百度统计账号 | 8 |  |
| 182 | 定制专题页项目类型表 | 8 |  |
| 183 | 操作记录表 | 8 |  |
| 184 | 耐克双倍配捐设置记录表 | 8 |  |
| 185 | 耐克在线推荐项目邮件发送记录 | 8 |  |
| 186 | 用户收藏的项目表 | 8 |  |
| 187 | 义卖奖券短信发送表 | 8 |  |
| 188 | 短信模板 | 7 |  |
| 189 | 义买快递费设置编辑表 | 7 |  |
| 190 | 义买快递费设置表 | 7 |  |
| 191 | 联劝网定时任务表 | 7 |  |
| 192 | 定时任务表 | 7 |  |
| 193 | 团体-队伍表 | 7 |  |
| 194 | 发票邮寄方式表 | 7 |  |
| 195 | 标签表 | 7 |  |
| 196 | 资源订阅分类表 | 7 |  |
| 197 | 公益时同步请求记录表 | 7 |  |
| 198 | 百度统计数据表 | 7 |  |
| 199 | 百度统计详情表 | 7 |  |
| 200 | 平台升级公告表 | 6 |  |
| 201 | 业务消息通知功能项表 | 6 |  |
| 202 | 公益项目外部情况表 | 6 |  |
| 203 | 月捐模板表 | 6 |  |
| 204 | 月捐模板图片表 | 6 |  |
| 205 | 日捐模板表 | 6 |  |
| 206 | 日捐模板图片表 | 6 |  |
| 207 | 消息通知表 | 6 |  |
| 208 | 发票申请周表定时生成记录 | 6 |  |
| 209 | 短信邮件发送平台回调代码表 | 6 |  |
| 210 | 常见问题的标签表 | 6 |  |
| 211 | 邮寄公司编码表 | 6 |  |
| 212 | 我的捐赠故事评分表 | 6 |  |
| 213 | 我的捐赠故事海报表 | 6 |  |
| 214 | 资源渠道关联表 | 6 |  |
| 215 | 公益三小时授权表 | 6 |  |
| 216 | 耐克FAQ | 6 |  |
| 217 | 用户密码错误记录 | 6 |  |
| 218 | 基本配置表 | 5 |  |
| 219 | 标签密钥表 | 5 |  |
| 220 | 短信发送记录表（用户手机） | 5 |  |
| 221 | 业务消息通知模板表 | 5 |  |
| 222 | 义卖订单存储过程执行错误记录表 | 5 |  |
| 223 | 定时器错误信息表 | 5 |  |
| 224 | 项目预算表 | 5 |  |
| 225 | 项目预算修改表 | 5 |  |
| 226 | 个人状态 | 5 |  |
| 227 | 配捐相关图片表 | 5 |  |
| 228 | 百度审核记录表 | 5 |  |
| 229 | 用户读取记录表 | 5 |  |
| 230 | 520夜跑定制表 | 5 |  |
| 231 | 资源标签关联表 | 5 |  |
| 232 | 资源订阅表 | 5 |  |
| 233 | 活动预算表 | 5 |  |
| 234 | 活动预算修改表 | 5 |  |
| 235 | 月捐预算表  | 5 |  |
| 236 | 月捐预算修改表  | 5 |  |
| 237 | 日捐预算表 | 5 |  |
| 238 | 日捐预算修改表 | 5 |  |
| 239 | 留言评价图片表 | 5 |  |
| 240 | 未成年肖像协议文件 | 5 |  |
| 241 | 耐克志愿者时长捐赠明细表 | 5 |  |
| 242 | 耐克企业微信通知模板表 | 5 |  |
| 243 | 邮件回复地址 | 4 |  |
| 244 | 投诉建议处理结果表 | 4 |  |
| 245 | 定时器扫描评分时间表 | 4 |  |
| 246 | imdb参数表 | 4 |  |
| 247 | 反馈图片表 | 4 |  |
| 248 | 联劝网引导页设置表 | 4 |  |
| 249 | 定时器信息表 | 4 |  |
| 250 | 短信类别  | 4 |  |
| 251 | 邮件类别  | 4 |  |
| 252 | 短信邮件发送平台表 | 4 |  |
| 253 | 短信邮件定时器配置表 | 4 |  |
| 254 | 发送记录表 | 4 |  |
| 255 | 敏感词内容表 | 4 |  |
| 256 | 机构项目外部筹款订单最后导入时间记录表 | 4 |  |
| 257 | 新闻动态附件关联表 | 4 |  |
| 258 | 联劝网系列浏览表 | 4 |  |
| 259 | 项目推送表 | 4 |  |
| 260 | 资源订阅分类关联表 | 4 |  |
| 261 | 发票同步事件表 | 4 |  |
| 262 | 电子劵基础表 | 4 |  |
| 263 | 义买活动电子劵记录明细表 | 4 |  |
| 264 | 志愿者小时数登记照片表 | 4 |  |
| 265 | 物资捐赠图片表 | 4 |  |
| 266 | 定时器表 | 3 |  |
| 267 | 业务消息通知功能项权限关系表 | 3 |  |
| 268 | 公益项目子母计划关系表 | 3 |  |
| 269 | 义买地域表 | 3 |  |
| 270 | 团体展示图表 | 3 |  |
| 271 | 标签和常见问题关联表 | 3 |  |
| 272 | 短链接码 | 3 |  |
| 273 | 百度统计url别名表 | 3 |  |
| 274 | 首页轮播图管理表 | 2 |  |
| 275 | 义卖订单存储过程标记表 | 2 |  |
| 276 | 用户手机号ID表 | 2 |  |
| 277 | 领域表 | 0 |  |
| 278 | 项目领域关联表 | 0 |  |
| 279 | 项目领域关联修改表 | 0 |  |
| 280 | 机构关心领域表 | 0 |  |

## 主要表结构详情

### 商户信息表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号 |
| 2 | institutionEntityGuid | varchar | 32 |  |  | 机构实体Id |
| 3 | status | int | 1 |  |  | 申请状态（0：已提交；1：申请成功：2：申请失败；99：未提交） |
| 4 | name | varchar | 20 |  |  | 联系人姓名 |
| 5 | telephone | varchar | 255 |  |  | 手机号号码 |
| 6 | email | varchar | 255 |  |  | 常用邮箱 |
| 7 | businessName | varchar | 255 |  |  | 商户简称 |
| 8 | businessType | int | 11 |  | 0 | 经营类目（公益） |
| 9 | scene | varchar | 255 |  |  | 售卖商品场景（1：线下场所；2：公众号；3：小程序；4：APP；5：网站；6：企业微信；多选） |
| 10 | shopAddress | varchar | 255 |  |  | 门店地址 |
| 11 | routine | varchar | 255 |  |  | 公众号/小程序名称 |
| 12 | routinePicId | varchar | 255 |  |  | 商品/服务的页面截图Id(可多张) |
| 13 | website | varchar | 255 |  |  | 公司网站 |
| 14 | appStatus | int | 11 |  |  | APP上线状态(1：已上线；2：未上线) |
| 15 | appPicId | varchar | 255 |  |  | APP页面截图Id(可多张) |
| 16 | appAddress | varchar | 255 |  |  | APP下载地址 |
| 17 | qualifications | int | 11 |  |  | 基金会法人登记证书文件Id |
| 18 | servicePhone | varchar | 255 |  |  | 客服电话 |
| 19 | otherPicId | varchar | 255 |  |  | 补充材料图片Id（可多张） |
| 20 | businessAddress | varchar | 255 |  |  | 注册地址 |
| 21 | businessWholeName | varchar | 255 |  |  | 商户名称 |
| 22 | cardBackId | int | 20 |  |  | 证件反面照片 |
| 23 | cardEndTime | datetime | 0 |  |  | 证件有效期结束日期 |
| 24 | cardFrontId | int | 20 |  |  | 证件正面照片 |
| 25 | cardName | varchar | 255 |  |  | 法定代表人姓名 |
| 26 | cardNo | varchar | 255 |  |  | 证件号码 |
| 27 | cardStartTime | datetime | 0 |  |  | 证件有效期开始日期 |
| 28 | cardType | int | 11 |  |  | 证件类型(1:身份证；2：护照；3：台胞证；4：中国香港居民-来往内地通行证；5：中国澳门居民-来往内地通行证；6：中国台湾居民-来往内地通行证) |
| 29 | cerificNumber | varchar | 255 |  |  | 证书号 |
| 30 | certificId | int | 20 |  |  | 证书图片id |
| 31 | endTime | datetime | 0 |  |  | 证书结束时间 |
| 32 | holderType | int | 11 |  |  | 企业法人/经办人(1:企业法人；2：经办人) |
| 33 | sphere | varchar | 255 |  |  | 业务范围 |
| 34 | startTime | datetime | 0 |  |  | 证书开始时间 |
| 35 | longTime | int | 11 |  |  | 证书是否长期 |
| 36 | cardLongTime | int | 11 |  |  | 证件是否长期（0：否；1：是） |
| 37 | bank | varchar | 255 |  |  | 开户银行 |
| 38 | bankProvince | varchar | 255 |  |  | 开户银行省 |
| 39 | bankCity | varchar | 255 |  |  | 开户银行市 |
| 40 | bankNo | varchar | 255 |  |  | 银行账号 |
| 41 | subBank | varchar | 255 |  |  | 开户支行 |
| 42 | otherBank | varchar | 255 |  |  | 开户支行其他 |
| 43 | createTime | datetime | 0 |  |  | 创建时间 |
| 44 | creator | varchar | 255 |  |  | 创建者 |
| 45 | auditor | varchar | 255 |  |  | 审核者 |
| 46 | auditTime | datetime | 0 |  |  | 审核时间 |
| 47 | reason | varchar | 255 |  |  | 审核理由 |
| 48 | certificType | int | 11 |  |  | 证书类型(0:已三证合一；1：未三证合一) |
| 49 | groupNumber | varchar | 255 |  |  | 组织机构代码 |
| 50 | groupId | int | 20 |  |  | 组织机构图片id |
| 51 | groupLongTime | int | 11 |  |  | 组织机构证书是否长期（0：否；1：是） |
| 52 | groupStartTime | datetime | 0 |  |  | 组织机构证书开始时间 |
| 53 | groupStartTime | datetime | 0 |  |  | 组织机构证书结束时间 |
| 54 | cardHolder | varchar | 255 |  |  | 证件持有人姓名 |
| 55 | benefitType | int | 11 |  |  | 是否为受益所有人(0:是；1：否,非经营者) |
| 56 | benefitCardType | int | 11 |  |  | 受益所有人证件类型(1:身份证；2：护照；3：台胞证；4：中国香港居民-来往内地通行证；5：中国澳门居民-来往内地通行证；6：中国台湾居民-来往内地通行证) |
| 57 | benefitCardFrontId | int | 20 |  |  | 受益所有人证件正面照片 |
| 58 | benefitCardBackId | int | 20 |  |  | 受益所有人证件反面照片 |
| 59 | benefitCardNo | varchar | 255 |  |  | 受益所有人证件号码 |
| 60 | benefitCardHolder | varchar | 255 |  |  | 受益所有人姓名 |
| 61 | benefitCardLongTime | int | 11 |  |  | 受益所有人证件是否长期（0：否；1：是） |
| 62 | benefitCardStartTime | datetime | 0 |  |  | 受益所有人证件有效期开始日期 |
| 63 | benefitCardEndTime | datetime | 0 |  |  | 受益所有人证件有效期结束日期 |
| 64 | shopName | varchar | 255 |  |  | 门店名称 |
| 65 | shopProvence | varchar | 255 |  |  | 门店省名 |
| 66 | shopCity | varchar | 255 |  |  | 门店市名 |
| 67 | shopPicId | int | 20 |  |  | 门店门头照片 |
| 68 | shopEnvId | int | 20 |  |  | 门店环境照片 |
| 69 | shopAppId | varchar | 255 |  |  | 线下场所对应的商家AppID |
| 70 | appIdType | int | 11 |  |  | 公众号类型（0：服务商；1：商家） |
| 71 | serviceAppId | varchar | 255 |  |  | 服务商公众号AppID |
| 72 | businessAppId | varchar | 255 |  |  | 商家公众号AppID |
| 73 | appIdPicId | varchar | 255 |  |  | 公众号页面截图Id(可多张) |
| 74 | appletAppId | varchar | 255 |  |  | 小程序AppID |
| 75 | appletPicId | varchar | 255 |  |  | 小程序截图Id(可多张) |
| 76 | applyType | int | 11 |  |  | app类型（0：服务商；1：商家） |
| 77 | serviceApplyId | varchar | 255 |  |  | 服务商应用AppID |
| 78 | businessApplyId | varchar | 255 |  |  | 商家应用AppID |
| 79 | applyPicId | varchar | 255 |  |  | app截图Id(可多张) |
| 80 | webAuthPicId | int | 20 |  |  | 网站授权函照片id |
| 81 | webAppId | varchar | 255 |  |  | 网站对应的商家AppID |
| 82 | corpID | varchar | 255 |  |  | 企业CorpID |
| 83 | corpPicId | varchar | 255 |  |  | 企业微信页面截图Id(可多张) |
| 84 | specialPicId | varchar | 255 |  |  | 特殊资质图Id(可多张) |
| 85 | adminType | int | 11 |  |  | 超管身份（0：法人/经营者；1：负责人） |
| 86 | adminName | varchar | 255 |  |  | 超管姓名 |
| 87 | adminInfoType | int | 11 |  |  | 超管资料类型（0：证件号码；1：微信openid） |
| 88 | adminNumber | varchar | 255 |  |  | 超管证件号码 |
| 89 | weixinOpenid | varchar | 255 |  |  | 微信openid |
| 90 | adminPhone | varchar | 255 |  |  | 手机号码 |
| 91 | adminMail | varchar | 255 |  |  | 邮箱 |

### 公益活动表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | activityId | varchar | 32 |  |  | 活动id（act开头) |
| 3 | activityName | varchar | 20 |  |  | 活动名称 |
| 4 | subjectPicId | int | 11 |  |  | 主题图片ID，列表页面（主题图片比例为8：9） |
| 5 | subjectBigPicId | int | 11 |  |  | 主题图片ID，详情页面（主题图片比例为4:3） |
| 6 | activityIntroduction | text |  |  |  | 活动简介 |
| 7 | startTime | datetime |  |  |  | 活动开始时间 |
| 8 | endTime | datetime |  |  |  | 活动结束时间 |
| 9 | status | int | 1 |  |  | 活动状态（0：未申请；1：在线申请中；2：在线上（审批通过）；3：中途取消；4：线下活动；5：审核不通过；6：已结束；7：测试发布；8：已删除；9：待认领；10：已认领；99：配置中；98：已配置） |
| 10 | fundraisingAccount | varchar | 32 |  |  | 筹款账户 |
| 11 | fundraisingStartTime | datetime |  |  |  | 筹款开始时间 |
| 12 | fundraisingEndTime | datetime |  |  |  | 筹款截止时间 |
| 13 | anonymousAccount | varchar | 32 |  |  | 匿名捐赠账户 |
| 14 | institutionId | int | 11 |  |  | 机构ID |
| 15 | reviewDetail | text |  |  |  | 审核不通过的理由 |
| 16 | reviewTime | datetime |  |  |  | 审核时间 |
| 17 | reviewer | varchar | 32 |  |  | 审核者（用户名） |
| 18 | reviewNickName | varchar | 32 |  |  | 审核者昵称 |
| 19 | applyTime | datetime |  |  |  | 申请时间 |
| 20 | isHaveWebSite | int | 1 |  |  | 是否有独立网站（0：有独立网站；1：没有独立网站） |
| 21 | webSiteUrl | varchar | 500 |  |  | 独立网站访问地址（PC版） |
| 22 | webSiteUrlM | varchar | 200 |  |  | 独立网站访问地址（PAD版、手机版） |
| 23 | showTime | datetime |  |  |  | 活动线上展示时间 |
| 24 | useAds | int | 1 |  |  | 报名类型（ 0：组队报名；1：不需要报名；2：个人报名；99：义卖活动） |
| 25 | createTime | datetime |  |  |  | 活动创建时间 |
| 26 | registrationAccount | varchar | 32 |  |  | 报名费账号（要在线报名并需要支付报名费时为必须项，审批通过时开通） |
| 27 | drawAccount | varchar | 32 |  |  | 抽签费账户 |
| 28 | version | varchar | 10 |  |  | 爱扑满版本号 |
| 29 | businessid | varchar | 32 |  |  | 感谢卡模板id |
| 30 | closer | varchar | 32 |  |  | 取消者（机构名称或管理员用户名） |
| 31 | closeReanson | varchar | 200 |  |  | 取消理由 |
| 32 | closeTime | datetime |  |  |  | 取消时间 |
| 33 | target1 | decimal | 15,2 |  |  | 筹款标的1 |
| 34 | target2 | decimal | 15,2 |  |  | 筹款标的2 |
| 35 | target3 | decimal | 15,2 |  |  | 筹款标的3 |
| 36 | indexShowOrder | int | 11 |  |  | 首页显示顺 |
| 37 | holderId | varvhar | 11 |  |  | 所属ID（认领公募基金会GUID） |
| 38 | claimStatus | int | 1 |  |  | 认领状态（0：未认领；1：已认领；2：自己发起（公募基金专用）） |
| 39 | contacts | varchar | 100 |  |  | 主办方联系方式/客服电话 |
| 40 | ifNeedFund | int | 1 |  |  | 是否需要筹款（0：要筹款；1：不要筹款） |
| 41 | showVideoFlag | int | 1 |  |  | 是否显示视频（0：不显示；1：显示） |
| 42 | videoId | String | 32 |  |  | 视频vid |
| 43 | videoSize | String | 32 |  |  | 视频大小 （含单位） |
| 44 | supportEn | int | 1 |  |  | 是否支持英文版（0：不支持；1：支持） |
| 45 | recordNumber | varchar | 255 |  |  | 备案编号 |
| 46 | logisticalAccount | varchar | 32 |  |  | 快递费账户（义买活动时存在） |
| 47 | lastEditStep | int | 2,0 |  |  | 上次关闭前最后修改的步骤 |
| 48 | fundraiseEntityGuid | varchar | 255 |  |  | 支付信息实体id |
| 49 | currentAccountType | int | 1 |  | 0 | 当前账户类型（0：未开；1：测试；2：正式） |
| 50 | isCollect | int | 1 |  | 0 | 是否收集用户信息 0不收集；1:收集； |
| 51 | lianQuanShowType | int | 1 |  |  | 在联劝网上的显示方式（0：慈善募捐；1：个人求助） |
| 52 | applyConfigTime | datetime |  |  |  | 申请配置的时间 |
| 53 | ifNeedActivityFund | int | 1 |  |  | 是否需要直捐给活动（0：需要；1：不需要；2：需要且初期为直捐活动） |
| 54 | details | varchar | 255 |  |  |  |
| 55 | venue | varchar | 255 |  |  |  |
| 56 | isMatch | int | 1 |  | 0 | 是否为需要配捐的活动（0：否；1：是） |
| 57 | ifNeedInsurance | int | 1 |  | 0 | 是否购买保险，0不购买，1购买 |
| 58 | updateTime | datetime |  |  |  | 更新时间 |
| 59 | numStartTime | date | 0 |  |  | 备案编号开始时间 |
| 60 | numEndTime | date | 0 |  |  | 备案编号结束时间 |
| 61 | numDetailUrl | date | 0 |  |  | 备案编号详情网址 |
| 62 | targetStr1 | varchar | 15 |  |  | 筹款标的1（别名） |
| 63 | targetStr2 | varchar | 15 |  |  | 筹款标的2（别名） |
| 64 | targetStr3 | varchar | 15 |  |  | 筹款标的3（别名） |
| 65 | targetflg | int | 1 |  | 0 | 标的别名标志（0：不使用；1：使用） |
| 66 | lastProgressTime | datetime | 0 |  |  | 最新的反馈提交日期 |
| 67 | remindProgressTime | datetime | 0 |  |  | 反馈提醒周期/结项逾期期限的开始时间 |
| 68 | isOss | int | 1 | 否 | 0 | 全局OSS标记 （0:未OSS化，1:已全部oss化） |
| 69 | isNeedCom | int | 1 | 否 | 0 | 是否需要商品义卖（0：否；1：是） |
| 70 | isRisk | int | 1 |  | 0 | 是否风控下线（0：否；1：是） |
| 71 | isOss1 | int | 1 | 否 | 0 | 小图OSS标记（0:未OSS化，1:已oss化） |
| 72 | isOss2 | int | 1 | 否 | 0 | 大图OSS标记（0:未OSS化，1:已oss化） |
| 73 | ossUrl1 | varchar | 255 |  |  | 小图OSS化的路径 |
| 74 | ossUrl2 | varchar | 255 |  |  | 大图OSS化的路径 |
| 75 | delflg1 | int | 1 | 否 | 0 | 小图OSS化删除标记（0:不需要处理，1:需要去oss端删除） |
| 76 | delflg2 | int | 1 | 否 | 0 | 大图OSS化删除标记（0:不需要处理，1:需要去oss端删除） |
| 77 | toolsOperateFlg | int | 1 | 否 | 0 | 爱扑满审核标志（0：不审核；1：审核） |
| 78 | fundraisingMoney | decimal | 15,2 | 是 | 0 | 资金同步筹款金额 |
| 79 | fundraisingCount | int | 11 | 是 | 0 | 资金同步筹款笔数 |
| 80 | fundMatch | decimal | 15,2 | 是 | 0 | 企业配捐金额 |
| 81 | countMatch | int | 11 | 是 | 0 | 企业配捐笔数 |
| 82 | amount | decimal | 15,2 | 是 | 0 | 预算总金额 |
| 83 | endProgressFlag | int | 1 | 是 | 0 | 是否有结项反馈（0：无 1：有） |
| 84 | portraitFileId | int |  |  |  | 未成年人肖像协议文件 |
| 85 | publicReview | int | 0 |  |  | 公募审核状态（0：公募无需审核、不审核；1：公募审核不通过；2：公募审核中；3：公募审核通过；）——已认领未上线的活动 |
| 86 | coverage | varchar | 1024 |  |  | 活动所在地域（地域ID的组合，用,连接。业务侧新增加的活动不允许为空） |
| 87 | donatePcFlg | int | 1 | 否 | 0 | 电脑端捐赠支持标志（0：不支持；1：支持） |

### 月捐表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号 |
| 2 | contractId | varchar | 32 | 否 |  | 月捐编号 |
| 3 | contractName | varchar | 32 | 否 |  | 月捐名称 |
| 4 | contractLogo | int | 11 | 是 |  | 主题LOGO |
| 5 | contractBigPicId | int | 11 | 是 |  | 主题图片ID，详情页面（主题图片比例为4:3） |
| 6 | contractIntroduction | text | 0 | 是 |  | 月捐简介 |
| 7 | contractDetail | text | 0 | 是 |  | 月捐详情 |
| 8 | contractTarget1 | decimal | 15 | 是 | 10 | 筹款标的1 |
| 9 | contractTarget2 | decimal | 15 | 是 | 20 | 筹款标的2 |
| 10 | contractTarget3 | decimal | 15 | 是 | 50 | 筹款标的3 |
| 11 | keywords1 | varchar | 5 | 是 |  | 关键字1 |
| 12 | keywords2 | varchar | 5 | 是 |  | 关键字2 |
| 13 | keywords3 | varchar | 5 | 是 |  | 关键字3 |
| 14 | status | int | 1 | 否 |  | 月捐状态（0：未申请；1：在线申请中；2：在线上（审批通过）；3：中途取消；5：审核不通过；6：已结束；8：已删除；9：待认领；10：已认领；） |
| 15 | institutionId | int | 11 | 是 |  | 机构ID |
| 16 | createTime | datetime | 0 | 是 |  | 创建时间 |
| 17 | applyTime | datetime | 0 | 是 |  | 发布时间 |
| 18 | applyer | varchar | 32 | 是 |  | 发布者（用户名） |
| 19 | applyNickName | varchar | 32 | 是 |  | applyNickName |
| 20 | cancelReason | varchar | 200 | 是 |  | 撤消理由 |
| 21 | cancelTime | datetime | 0 | 是 |  | 撤消时间 |
| 22 | indexShowOrder | int | 11 | 是 |  | 首页显示顺 |
| 23 | accountContract | varchar | 32 | 是 |  | 月捐账户 |
| 24 | fundraisingAccount | varchar | 32 | 是 |  | 筹款账户 |
| 25 | anonymousAccount | varchar | 32 | 是 |  | 匿名捐赠账户 |
| 26 | wxPlanId | varchar | 28 | 是 |  | 微信月捐模板ID |
| 27 | recordNumber | varchar | 255 | 是 |  | 备案编号 |
| 28 | lastEditStep | int | 11 | 是 |  | 上次关闭前最后修改的步骤 |
| 29 | isCollect | int | 11 | 是 |  | 是否收集用户信息 1:收集；0不收集 |
| 30 | contacts | varchar | 255 | 是 |  | 主办方联系方式/客服电话 |
| 31 | minDonationAmount | decimal | 15 | 是 |  | 起捐金额 |
| 32 | holderId | varchar | 32 | 是 |  | 所属ID（机构EntityGuid） |
| 33 | claimStatus | int | 2 | 是 |  | 认领状态（0：未认领；1：已认领；2：自己发起（公募基金专用）） |
| 34 | fundraiseEntityGuid | varchar | 32 | 是 |  | 付款信息guid |
| 35 | OnlineTime | datetime | 0 | 是 |  | 上线时间 |
| 36 | closeReason | varchar | 200 | 是 |  | 结束理由 |
| 37 | closer | varchar | 32 | 是 |  | 结束机构 |
| 38 | closeTime | datetime | 0 | 是 |  | 结束时间 |
| 39 | reviewDetail | varchar | 200 | 是 |  | 审核意见 |
| 40 | reviewTime | datetime | 0 | 是 |  | 审核时间 |
| 41 | reviewer | varchar | 32 | 是 |  | 审核者用户名 |
| 42 | reviewNickName | varchar | 32 | 是 |  | 审核者昵称 |
| 43 | lianQuanShowType | int | 1 | 是 |  | 在联劝网上的显示方式（0：慈善募捐；1：个人求助） |
| 44 | isSingleDonate | int | 2 | 是 |  | 是否需要单笔捐（1：需要；2：不需要） |
| 45 | contractAbbreviation | varchar | 12 | 是 |  | 月捐简称（发短信用） |
| 46 | showFlag | int | 2 | 是 | 0 | 是否展示（0：不展示，1：展示） |
| 47 | score | demical | 11,2 | 是 |  | 活动/项目评分，保留一位小数 |
| 48 | sortFlag | int | 2 | 是 | 0 | 是否优先排序（新活动），0是1不是 |
| 49 | showIndex | int | 11 | 是 | 0 | 排序编号 |
| 50 | updateTime | datetime |  |  |  | 更新时间 |
| 51 | isMatch | int | 1 |  | 0 | 是否有配捐（0：否；1：是） |
| 52 | numStartTime | date | 0 |  |  | 备案编号开始时间 |
| 53 | numEndTime | date | 0 |  |  | 备案编号结束时间 |
| 54 | numDetailUrl | date | 0 |  |  | 备案编号详情网址 |
| 55 | targetStr1 | varchar | 15 |  |  | 筹款标的1（别名） |
| 56 | targetStr1 | varchar | 15 |  |  | 筹款标的2（别名） |
| 57 | targetStr1 | varchar | 15 |  |  | 筹款标的2（别名） |
| 58 | targetflg | int | 1 |  | 0 | 标的别名标志（0：不使用；1：使用） |
| 59 | lastProgressTime | datetime | 0 |  |  | 最新的反馈提交日期 |
| 60 | remindProgressTime | datetime | 0 |  |  | 反馈提醒周期/结项逾期期限的开始时间 |
| 61 | isOss | int | 1 | 否 | 0 | 全局OSS标记 （0:未OSS化，1:已全部oss化） |
| 62 | isRisk | int | 1 |  | 0 | 是否风控下线（0：否；1：是） |
| 63 | isOss1 | int | 1 | 否 | 0 | 小图OSS标记（0:未OSS化，1:已oss化） |
| 64 | isOss2 | int | 1 | 否 | 0 | 大图OSS标记（0:未OSS化，1:已oss化） |
| 65 | ossUrl1 | varchar | 255 |  |  | 小图OSS化的路径 |
| 66 | ossUrl2 | varchar | 255 |  |  | 大图OSS化的路径 |
| 67 | delflg1 | int | 1 | 否 | 0 | 小图OSS化删除标记（0:不需要处理，1:需要去oss端删除） |
| 68 | delflg2 | int | 1 | 否 | 0 | 大图OSS化删除标记（0:不需要处理，1:需要去oss端删除） |
| 69 | fundraisingMoney | decimal | 15,2 | 是 | 0 | 资金同步筹款金额 |
| 70 | fundraisingCount | int | 11 | 是 | 0 | 资金同步筹款笔数 |
| 71 | contractNumber | int | 11 | 是 | 0 | 月捐人数 |
| 72 | accountContractMoney | decimal | 15,2 | 是 | 0 | 月捐总金额 |
| 73 | autoInvoice | int | 1 | 否 | 1 | 是否设置开票方式（0：否（默认手动申请推送）；1：是） 设置为0时，开票方式要设置为3 |
| 74 | customPoster | int | 1 | 否 | 0 | 自定义海报（0：否；1：是） |
| 75 | firstOnlineTime | datetime | 0 |  |  | 第一次上线时间 |
| 76 | invoiceMethod | int | 0 |  | 1 | 开票方式(0：默认无意义；1:月度自动开票；2：年度整合自动开票；3：手动申请开票；） |
| 77 | amount | decimal | 15,2 | 是 | 0 | 预算总金额 |
| 78 | fundraisingStartTime | date | 0 |  |  | 筹款开始时间 |
| 79 | fundraisingEndTime | date | 0 |  |  | 筹款结束时间 |
| 80 | endProgressFlag | int | 1 | 是 | 0 | 是否有结项反馈（0：无 1：有） |
| 81 | portraitFileId | int |  |  |  | 未成年人肖像协议文件 |
| 82 | publicReview | int | 0 |  |  | 公募审核状态（0：公募无需审核、不审核；1：公募审核不通过；2：公募审核中；3：公募审核通过；）——已认领未上线的月捐 |
| 83 | coverage | varchar | 255 |  |  | 月捐所在地域（地域ID的组合，用,连接。业务侧新增加的月捐不允许为空） |
| 84 | toolsOperateFlg | int | 0 |  |  | 月捐邀请审核标志（0：不审核；1：审核） |
| 85 | supportEn | int | 0 | 是 | 0 | 是否支持英文版（0：不支持；1：支持） |
| 86 | donatePcFlg | int | 1 | 否 | 0 | 电脑端捐赠支持标志（0：不支持；1：支持） |

### 公益项目表 

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | projectId | varchar | 32 |  |  | 项目ID(PDT开头） |
| 3 | projectName | varchar | 64 |  |  | 项目名称 |
| 4 | cycle | int | 1 |  |  | 项目周期（1:1个月   2:2个月   3:3个月  6:6个月   12:12个月   99：无期限） |
| 5 | fundraisingMaxAmount | decimal | 15,2 |  |  | 筹款目标（根据项目筹款结束方式设置，可以为空） |
| 6 | projectFundStart | datetime |  |  |  | 项目筹款开始时间 |
| 7 | projectFundEnd | datetime |  |  |  | 项目筹款结束时间（根据项目筹款结束方式设置，可以为空） |
| 8 | reason | text |  |  |  | 发起缘由 |
| 9 | projectBigPicId | int | 11 |  |  | 项目主题大图（图片ID，详情页面显示） |
| 10 | projectSmallPicId | int | 11 |  |  | 项目主题小图（图片ID，首页和列表页面显示） |
| 11 | projectIntroduction | varchar | 100 |  |  | 项目简介 |
| 12 | projectTarget1 | decimal | 15,2 |  |  | 项目标的1 |
| 13 | projectTarget2 | decimal | 15,2 |  |  | 项目标的2 |
| 14 | projectTarget3 | decimal | 15,2 |  |  | 项目标的3 |
| 15 | status | int | 1 |  |  | 项目状态（0：草稿；1：审核中；2：进行中；3：审核不通过；4：已关闭（中途取消）；5:第三方平台；6：已结束；8：已删除；9：待认领；10：已认领；） |
| 16 | institutionId | int | 11 |  |  | 机构ID |
| 17 | holderId | int | 11 |  |  | 所属ID（填写所属活动ID或所属基金ID、所属项目ID） |
| 18 | type | int | 1 |  |  | 所属类型（0：联劝网项目；1：外部接口项目；3：基金（2018年5月平台分离前历史数据）4：母计划项目；5：子计划项目） |
| 19 | claimStatus | int | 1 |  |  | 认领状态（0：未认领；1：已认领；2：自己发起（公募基金专用）） |
| 20 | amount | decimal | 15,2 |  |  | 预算总额 |
| 21 | reviewDetail | varchar | 200 |  |  | 审核不通过的理由 |
| 22 | reviewTime | datetime |  |  |  | 审核时间 |
| 23 | reviewer | varchar | 32 |  |  | 审核者（用户名） |
| 24 | reviewNickName | varchar | 32 |  |  | 审核者昵称 |
| 25 | applyTime | datetime |  |  |  | 申请时间 |
| 26 | insertTime | datetime |  |  |  | 创建时间 |
| 27 | closeTime | datetime |  |  |  | 结束筹款时间 |
| 28 | sortNo | int | 11 |  |  | 排序（联劝网上的显示规则） |
| 29 | fundraisingAccount | varchar | 32 |  |  | 筹款账户 |
| 30 | closeReason | varchar | 200 |  |  | 结束筹款理由 |
| 31 | closer | varchar | 32 |  |  | 结束者（机构名称或管理员用户名） |
| 32 | anonymousAccount | varchar | 100 |  |  | 匿名账户 |
| 33 | ifFullStop | int | 1 |  |  | 筹满截止标志（0：不截止；1：截止） |
| 34 | webSite | varchar | 255 |  |  | 筹款网址 |
| 35 | showListFlag | int | 1 |  |  | 联劝网列表是否展示（0：不展示；1：展示） |
| 36 | showIndexFlag | int | 1 |  |  | 联劝网首页是否展示（0：不展示；1：展示） |
| 37 | mark | int | 1 |  |  | 筹款是否已结束的标志位（0：未筹款；1：筹款中；2：筹款结束；） |
| 38 | markOfEndTime | datetime |  |  |  | 筹款结束标志位更新时间（mark值更新为2时的时间） |
| 39 | coverage | varchar | 255 |  |  | 项目所在地域（地域ID的组合，用,连接。业务侧新增加的项目不允许为空） |
| 40 | ifSupportEpClaim | int | 1 |  |  | 是否支持企业认领（0：不支持；1：支持） |
| 41 | recordNumber | varchar | 255 |  |  | 备案编号 |
| 42 | createStep | int | 1 |  |  | 创建步数（1：编辑项目；2：上传图片；3：编辑详情；4：编制预算；5：收集信息；6：提交审核） |
| 43 | currentAccountType | int | 1 |  | 0 | 当前账户类型（0：未开；1：测试；2：正式） |
| 44 | fundraiseEntityGuid | varchar | 255 |  |  | 付款信息guid |
| 45 | isCollect | int | 1 |  |  | 是否收集用户信息 1:收集；2不收集 |
| 46 | lianQuanShowType | int | 1 |  |  | 在联劝网上的显示方式（0：慈善募捐；1：个人求助） |
| 47 | showIndex | int | 11 |  |  | 优先排序标记 |
| 48 | callbackurl | varchar | 255 |  |  | 回调URL，用于项目上线后通知 |
| 49 | updateTime | datetime |  |  |  | 更新时间 |
| 50 | isMatch | int | 1 |  | 0 | 是否有配捐（0：否；1：是） |
| 51 | numStartTime | date | 0 |  |  | 备案编号开始时间 |
| 52 | numEndTime | date | 0 |  |  | 备案编号结束时间 |
| 53 | numDetailUrl | date | 0 |  |  | 备案编号详情网址 |
| 54 | targetStr1 | varchar | 15 |  |  | 筹款标的1（别名） |
| 55 | targetStr1 | varchar | 15 |  |  | 筹款标的2（别名） |
| 56 | targetStr1 | varchar | 15 |  |  | 筹款标的2（别名） |
| 57 | targetflg | int | 1 |  | 0 | 标的别名标志（0：不使用；1：使用） |
| 58 | lastProgressTime | datetime | 0 |  |  | 最新的反馈提交日期 |
| 59 | remindProgressTime | datetime | 0 |  |  | 反馈提醒周期/结项逾期期限的开始时间 |
| 60 | isOss | int | 1 | 否 | 0 | 全局OSS标记 （0:未OSS化，1:已全部oss化） |
| 61 | logisticalAccount | varchar | 32 |  |  | 快递费账户（需要商品义买时存在） |
| 62 | isNeedCom | int | 1 | 否 | 0 | 是否需要商品义卖（0：否；1：是） |
| 63 | isRisk | int | 1 |  | 0 | 是否风控下线（0：否；1：是） |
| 64 | isOss1 | int | 1 | 否 | 0 | 小图OSS标记（0:未OSS化，1:已oss化） |
| 65 | isOss2 | int | 1 | 否 | 0 | 大图OSS标记（0:未OSS化，1:已oss化） |
| 66 | ossUrl1 | varchar | 255 |  |  | 小图OSS化的路径 |
| 67 | ossUrl2 | varchar | 255 |  |  | 大图OSS化的路径 |
| 68 | delflg1 | int | 1 | 否 | 0 | 小图OSS化删除标记（0:不需要处理，1:需要去oss端删除） |
| 69 | delflg2 | int | 1 | 否 | 0 | 大图OSS化删除标记（0:不需要处理，1:需要去oss端删除） |
| 70 | toolsOperateFlg | int | 1 | 否 | 0 | 爱扑满审核标志（0：不审核；1：审核） |
| 71 | fundraisingMoney | decimal | 15,2 | 是 | 0 | 资金同步筹款金额 |
| 72 | fundraisingCount | int | 11 | 是 | 0 | 资金同步筹款笔数 |
| 73 | fundMatch | decimal | 15,2 | 是 | 0 | 企业配捐金额 |
| 74 | countMatch | int | 11 | 是 | 0 | 企业配捐笔数 |
| 75 | stopAccountFlag | int | 1 |  | 0 | 筹满截止操作记录（0：未操作；1：已操作，账户停止筹款；2：账户已修改为其他状态） |
| 76 | endProgressFlag | int | 1 | 是 | 0 | 是否有结项反馈（0：无 1：有） |
| 77 | portraitFileId | int |  |  |  | 未成年人肖像协议文件 |
| 78 | publicReview | int | 0 |  |  | 公募审核状态（0：公募无需审核、不审核；1：公募审核不通过；2：公募审核中；3：公募审核通过；）——已认领未上线的项目 |
| 79 | subProReviewIns | String | 32 | 是 |  | 子计划审核机构（母计划的创建机构） |
| 80 | donatePcFlg | int | 1 | 否 | 0 | 电脑端捐赠支持标志（0：不支持；1：支持） |

### 日捐表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号 |
| 2 | contractId | varchar | 32 | 否 |  | 日捐编号 |
| 3 | contractName | varchar | 32 | 否 |  | 日捐名称 |
| 4 | contractLogo | int | 11 | 是 |  | 主题LOGO |
| 5 | contractBigPicId | int | 11 | 是 |  | 主题图片ID，详情页面（主题图片比例为4:3） |
| 6 | contractIntroduction | text | 0 | 是 |  | 日捐简介 |
| 7 | contractTarget1 | decimal | 15 | 是 | 10 | 筹款标的1 |
| 8 | contractTarget2 | decimal | 15 | 是 | 20 | 筹款标的2 |
| 9 | contractTarget3 | decimal | 15 | 是 | 50 | 筹款标的3 |
| 10 | status | int | 1 | 否 |  | 日捐状态（0：未申请；1：在线申请中；2：在线上（审批通过）；3：中途取消；5：审核不通过；6：已结束；8：已删除；9：待认领；10：已认领；） |
| 11 | institutionId | int | 11 | 是 |  | 机构ID |
| 12 | createTime | datetime | 0 | 是 |  | 创建时间 |
| 13 | applyTime | datetime | 0 | 是 |  | 发布时间 |
| 14 | applyer | varchar | 32 | 是 |  | 发布者（用户名） |
| 15 | applyNickName | varchar | 32 | 是 |  | applyNickName |
| 16 | cancelReason | varchar | 200 | 是 |  | 撤消理由 |
| 17 | cancelTime | datetime | 0 | 是 |  | 撤消时间 |
| 18 | indexShowOrder | int | 11 | 是 |  | 首页显示顺 |
| 19 | accountcontract | varchar | 32 | 是 |  | 日捐账户 |
| 20 | fundraisingAccount | varchar | 32 | 是 |  | 筹款账户 |
| 21 | anonymousAccount | varchar | 32 | 是 |  | 匿名捐赠账户 |
| 22 | wxPlanId | varchar | 28 | 是 |  | 微信日捐模板ID |
| 23 | recordNumber | varchar | 255 | 是 |  | 备案编号 |
| 24 | lastEditStep | int | 11 | 是 |  | 上次关闭前最后修改的步骤 |
| 25 | isCollect | int | 11 | 是 |  | 是否收集用户信息 1:收集；0不收集 |
| 26 | contacts | varchar | 255 | 是 |  | 主办方联系方式/客服电话 |
| 27 | minDonationAmount | decimal | 15 | 是 |  | 起捐金额 |
| 28 | holderId | varchar | 32 | 是 |  | 所属ID（机构EntityGuid） |
| 29 | claimStatus | int | 2 | 是 |  | 认领状态（0：未认领；1：已认领；2：自己发起（公募基金专用）） |
| 30 | fundraiseEntityGuid | varchar | 32 | 是 |  | 付款信息guid |
| 31 | onlineTime | datetime | 0 | 是 |  | 上线时间 |
| 32 | closeReason | varchar | 200 | 是 |  | 结束理由 |
| 33 | closer | varchar | 32 | 是 |  | 结束机构 |
| 34 | closeTime | datetime | 0 | 是 |  | 结束时间 |
| 35 | reviewDetail | varchar | 200 | 是 |  | 审核意见 |
| 36 | reviewTime | datetime | 0 | 是 |  | 审核时间 |
| 37 | reviewer | varchar | 32 | 是 |  | 审核者用户名 |
| 38 | reviewNickName | varchar | 32 | 是 |  | 审核者昵称 |
| 39 | lianQuanShowType | int | 1 | 是 |  | 在联劝网上的显示方式（0：慈善募捐；1：个人求助） |
| 40 | isSingleDonate | int | 2 | 是 |  | 是否需要单笔捐（1：需要；2：不需要） |
| 41 | contractAbbreviation | varchar | 12 | 是 |  | 日捐简称（发短信用） |
| 42 | showFlag | int | 2 | 是 | 0 | 是否展示（0：不展示，1：展示） |
| 43 | showIndex | int | 11 | 是 | 0 | 排序编号 |
| 44 | updateTime | datetime |  |  |  | 更新时间 |
| 45 | isMatch | int | 1 |  | 0 | 是否有配捐（0：否；1：是） |
| 46 | numStartTime | date | 0 |  |  | 备案编号开始时间 |
| 47 | numEndTime | date | 0 |  |  | 备案编号结束时间 |
| 48 | numDetailUrl | date | 0 |  |  | 备案编号详情网址 |
| 49 | targetStr1 | varchar | 15 |  |  | 筹款标的1（别名） |
| 50 | targetStr2 | varchar | 15 |  |  | 筹款标的2（别名） |
| 51 | targetStr3 | varchar | 15 |  |  | 筹款标的2（别名） |
| 52 | targetflg | int | 1 |  | 0 | 标的别名标志（0：不使用；1：使用） |
| 53 | lastProgressTime | datetime | 0 |  |  | 最新的反馈提交日期 |
| 54 | remindProgressTime | datetime | 0 |  |  | 反馈提醒周期/结项逾期期限的开始时间 |
| 55 | isOss | int | 1 | 否 | 0 | 全局OSS标记 （0:未OSS化，1:已全部oss化） |
| 56 | isRisk | int | 1 |  | 0 | 是否风控下线（0：否；1：是） |
| 57 | isOss1 | int | 1 | 否 | 0 | 小图OSS标记（0:未OSS化，1:已oss化） |
| 58 | isOss2 | int | 1 | 否 | 0 | 大图OSS标记（0:未OSS化，1:已oss化） |
| 59 | ossUrl1 | varchar | 255 |  |  | 小图OSS化的路径 |
| 60 | ossUrl2 | varchar | 255 |  |  | 大图OSS化的路径 |
| 61 | delflg1 | int | 1 | 否 | 0 | 小图OSS化删除标记（0:不需要处理，1:需要去oss端删除） |
| 62 | delflg2 | int | 1 | 否 | 0 | 大图OSS化删除标记（0:不需要处理，1:需要去oss端删除） |
| 63 | fundraisingMoney | decimal | 15,2 | 是 | 0 | 资金同步筹款金额 |
| 64 | fundraisingCount | int | 11 | 是 | 0 | 资金同步筹款笔数 |
| 65 | contractNumber | int | 11 | 是 | 0 | 月捐人数 |
| 66 | accountContractMoney | decimal | 15,2 | 是 | 0 | 月捐总金额 |
| 67 | amount | decimal | 15,2 | 是 | 0 | 预算总金额 |
| 68 | fundraisingStartTime | date | 0 |  |  | 筹款开始时间 |
| 69 | fundraisingEndTime | date | 0 |  |  | 筹款结束时间 |
| 70 | endProgressFlag | int | 1 | 是 | 0 | 是否有结项反馈（0：无 1：有） |
| 71 | portraitFileId | int |  |  |  | 未成年人肖像协议文件 |
| 72 | publicReview | int | 0 |  |  | 公募审核状态（0：公募无需审核、不审核；1：公募审核不通过；2：公募审核中；3：公募审核通过；）——已认领未上线的日捐 |
| 73 | coverage | varchar | 255 |  |  | 日捐所在地域（地域ID的组合，用,连接。业务侧新增加的日捐不允许为空） |
| 74 | donatePcFlg | int | 1 | 否 | 0 | 电脑端捐赠支持标志（0：不支持；1：支持） |

### 公益活动编辑表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | activityId | varchar | 32 |  |  | 活动id（act开头) |
| 3 | activityName | varchar | 20 |  |  | 活动名称 |
| 4 | subjectPicId | int | 11 |  |  | 主题图片ID，列表页面（主题图片比例为8：9） |
| 5 | subjectBigPicId | int | 11 |  |  | 主题图片ID，详情页面（主题图片比例为4:3） |
| 6 | activityIntroduction | text |  |  |  | 活动简介 |
| 7 | startTime | datetime |  |  |  | 活动开始时间 |
| 8 | endTime | datetime |  |  |  | 活动结束时间 |
| 9 | status | int | 1 |  |  | 编辑状态（0：公募修改中；1：公募提交审核；2：机构修改中；3：机构提交审核；5：公募审核不通过；6：机构审核不通过） |
| 10 | fundraisingAccount | varchar | 32 |  |  | 筹款账户 |
| 11 | fundraisingStartTime | datetime |  |  |  | 筹款开始时间 |
| 12 | fundraisingEndTime | datetime |  |  |  | 筹款截止时间 |
| 13 | anonymousAccount | varchar | 32 |  |  | 匿名捐赠账户 |
| 14 | institutionId | int | 11 |  |  | 机构ID |
| 15 | reviewDetail | text |  |  |  | 审核不通过的理由 |
| 16 | reviewTime | datetime |  |  |  | 审核时间 |
| 17 | reviewer | varchar | 32 |  |  | 审核者（用户名） |
| 18 | reviewNickName | varchar | 32 |  |  | 审核者昵称 |
| 19 | applyTime | datetime |  |  |  | 申请时间 |
| 20 | isHaveWebSite | int | 1 |  |  | 是否有独立网站（0：有独立网站；1：没有独立网站） |
| 21 | webSiteUrl | varchar | 500 |  |  | 独立网站访问地址（PC版） |
| 22 | webSiteUrlM | varchar | 200 |  |  | 独立网站访问地址（PAD版、手机版） |
| 23 | showTime | datetime |  |  |  | 活动线上展示时间 |
| 24 | useAds | int | 1 |  |  | 报名类型（ 0：组队报名；1：不需要报名；2：个人报名；99：义卖活动） |
| 25 | createTime | datetime |  |  |  | 活动创建时间 |
| 26 | registrationAccount | varchar | 32 |  |  | 报名费账号（要在线报名并需要支付报名费时为必须项，审批通过时开通） |
| 27 | drawAccount | varchar | 32 |  |  | 抽签费账户 |
| 28 | version | varchar | 10 |  |  | 爱扑满版本号 |
| 29 | businessid | varchar | 32 |  |  | 感谢卡模板id |
| 30 | closer | varchar | 32 |  |  | 取消者（机构名称或管理员用户名） |
| 31 | closeReanson | varchar | 200 |  |  | 取消理由 |
| 32 | closeTime | datetime |  |  |  | 取消时间 |
| 33 | target1 | decimal | 15,2 |  |  | 筹款标的1 |
| 34 | target2 | decimal | 15,2 |  |  | 筹款标的2 |
| 35 | target3 | decimal | 15,2 |  |  | 筹款标的3 |
| 36 | indexShowOrder | int | 11 |  |  | 首页显示顺 |
| 37 | holderId | varvhar | 11 |  |  | 所属ID（认领公募基金会GUID） |
| 38 | claimStatus | int | 1 |  |  | 认领状态（0：未认领；1：已认领；2：自己发起（公募基金专用）） |
| 39 | contacts | varchar | 100 |  |  | 主办方联系方式/客服电话 |
| 40 | ifNeedFund | int | 1 |  |  | 是否需要筹款（0：要筹款；1：不要筹款） |
| 41 | showVideoFlag | int | 1 |  |  | 是否显示视频（0：不显示；1：显示） |
| 42 | videoId | String | 32 |  |  | 视频vid |
| 43 | supportEn | int | 1 |  |  | 是否支持英文版（0：不支持；1：支持） |
| 44 | recordNumber | varchar | 255 |  |  | 备案编号 |
| 45 | logisticalAccount | varchar | 32 |  |  | 快递费账户（义买活动时存在） |
| 46 | lastEditStep | int | 2,0 |  |  | 上次关闭前最后修改的步骤 |
| 47 | fundraiseEntityGuid | varchar | 255 |  |  | 支付信息实体id |
| 48 | currentAccountType | int | 1 |  | 0 | 当前账户类型（0：未开；1：测试；2：正式） |
| 49 | isCollect | int | 1 |  | 0 | 是否收集用户信息 0不收集；1:收集； |
| 50 | lianQuanShowType | int | 1 |  |  | 在联劝网上的显示方式（0：慈善募捐；1：个人求助） |
| 51 | applyConfigTime | datetime |  |  |  | 申请配置的时间 |
| 52 | ifNeedActivityFund | int | 1 |  |  | 是否需要直捐给活动（0：需要；1：不需要；2：需要且初期为直捐活动） |
| 53 | details | varchar | 255 |  |  |  |
| 54 | venue | varchar | 255 |  |  |  |
| 55 | isMatch | int | 1 |  | 0 | 是否为需要配捐的活动（0：否；1：是） |
| 56 | ifNeedInsurance | int | 1 |  | 0 | 是否购买保险，0不购买，1购买 |
| 57 | updateTime | datetime |  |  |  | 更新时间 |
| 58 | numStartTime | date | 0 |  |  | 备案编号开始时间 |
| 59 | numEndTime | date | 0 |  |  | 备案编号结束时间 |
| 60 | numDetailUrl | date | 0 |  |  | 备案编号详情网址 |
| 61 | targetStr1 | varchar | 15 |  |  | 筹款标的1（别名） |
| 62 | targetStr1 | varchar | 15 |  |  | 筹款标的2（别名） |
| 63 | targetStr1 | varchar | 15 |  |  | 筹款标的2（别名） |
| 64 | targetflg | int | 1 |  | 0 | 标的别名标志（0：不使用；1：使用） |
| 65 | isNeedCom | int | 1 | 否 | 0 | 是否需要商品义卖（0：否；1：是） |
| 66 | fundraisingMoney | decimal | 15,2 |  | 0 | 资金同步筹款金额 |
| 67 | fundraisingCount | int | 11 |  | 0 | 资金同步筹款笔数 |
| 68 | fundMatch | decimal | 15,2 |  | 0 | 企业配捐金额 |
| 69 | countMatch | int | 11 |  | 0 | 企业配捐笔数 |
| 70 | amount | decimal | 15,2 | 是 | 0 | 预算总金额 |
| 71 | portraitFileId | int |  |  |  | 未成年人肖像协议文件 |
| 72 | coverage | varchar | 1024 |  |  | 活动所在地域（地域ID的组合，用,连接。业务侧新增加的活动不允许为空） |

### 月捐编辑表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号 |
| 2 | contractId | varchar | 32 | 否 |  | 月捐编号 |
| 3 | contractName | varchar | 32 | 否 |  | 月捐名称 |
| 4 | contractLogo | int | 11 | 是 |  | 主题LOGO |
| 5 | contractBigPicId | int | 11 | 是 |  | 主题图片ID，详情页面（主题图片比例为4:3） |
| 6 | contractIntroduction | text | 0 | 是 |  | 月捐简介 |
| 7 | contractDetail | text | 0 | 是 |  | 月捐详情 |
| 8 | contractTarget1 | decimal | 15 | 是 | 10 | 筹款标的1 |
| 9 | contractTarget2 | decimal | 15 | 是 | 20 | 筹款标的2 |
| 10 | contractTarget3 | decimal | 15 | 是 | 50 | 筹款标的3 |
| 11 | keywords1 | varchar | 5 | 是 |  | 关键字1 |
| 12 | keywords2 | varchar | 5 | 是 |  | 关键字2 |
| 13 | keywords3 | varchar | 5 | 是 |  | 关键字3 |
| 14 | status | int | 1 | 否 |  | 编辑状态（0：认领机构修改中；1：认领机构提交审核；2：创建机构修改中；3：创建机构提交审核；5：认领机构审核不通过；6：创建机构审核不通过） |
| 15 | institutionId | int | 11 | 是 |  | 机构ID |
| 16 | createTime | datetime | 0 | 是 |  | 创建时间 |
| 17 | applyTime | datetime | 0 | 是 |  | 发布时间 |
| 18 | applyer | varchar | 32 | 是 |  | 发布者（用户名） |
| 19 | applyNickName | varchar | 32 | 是 |  | applyNickName |
| 20 | cancelReason | varchar | 200 | 是 |  | 撤消理由 |
| 21 | cancelTime | datetime | 0 | 是 |  | 撤消时间 |
| 22 | indexShowOrder | int | 11 | 是 |  | 首页显示顺 |
| 23 | accountContract | varchar | 32 | 是 |  | 月捐账户 |
| 24 | fundraisingAccount | varchar | 32 | 是 |  | 筹款账户 |
| 25 | anonymousAccount | varchar | 32 | 是 |  | 匿名捐赠账户 |
| 26 | wxPlanId | varchar | 28 | 是 |  | 微信月捐模板ID |
| 27 | recordNumber | varchar | 255 | 是 |  | 备案编号 |
| 28 | lastEditStep | int | 11 | 是 |  | 上次关闭前最后修改的步骤 |
| 29 | isCollect | int | 11 | 是 |  | 是否收集用户信息 1:收集；0不收集 |
| 30 | contacts | varchar | 255 | 是 |  | 主办方联系方式/客服电话 |
| 31 | minDonationAmount | decimal | 15 | 是 |  | 起捐金额 |
| 32 | holderId | varchar | 32 | 是 |  | 所属ID（机构EntityGuid） |
| 33 | claimStatus | int | 2 | 是 |  | 认领状态（0：未认领；1：已认领；2：自己发起（公募基金专用）） |
| 34 | fundraiseEntityGuid | varchar | 32 | 是 |  | 付款信息guid |
| 35 | OnlineTime | datetime | 0 | 是 |  | 上线时间 |
| 36 | closeReason | varchar | 200 | 是 |  | 结束理由 |
| 37 | closer | varchar | 32 | 是 |  | 结束机构 |
| 38 | closeTime | datetime | 0 | 是 |  | 结束时间 |
| 39 | reviewDetail | varchar | 200 | 是 |  | 审核意见 |
| 40 | reviewTime | datetime | 0 | 是 |  | 审核时间 |
| 41 | reviewer | varchar | 32 | 是 |  | 审核者用户名 |
| 42 | reviewNickName | varchar | 32 | 是 |  | 审核者昵称 |
| 43 | lianQuanShowType | int | 1 | 是 |  | 在联劝网上的显示方式（0：慈善募捐；1：个人求助） |
| 44 | isSingleDonate | int | 2 | 是 |  | 是否需要单笔捐（1：需要；2：不需要） |
| 45 | contractAbbreviation | varchar | 12 | 是 |  | 月捐简称（发短信用） |
| 46 | showFlag | int | 2 | 是 | 0 | 是否展示 |
| 47 | score | demical | 11,2 | 是 |  | 活动/项目评分，保留一位小数 |
| 48 | sortFlag | int | 2 | 是 | 0 | 是否优先排序（新活动），0是1不是 |
| 49 | showIndex | int | 11 | 是 | 0 | 排序编号 |
| 50 | updateTime | datetime |  |  |  | 更新时间 |
| 51 | isMatch | int | 1 |  | 0 | 是否有配捐（0：否；1：是） |
| 52 | numStartTime | date | 0 |  |  | 备案编号开始时间 |
| 53 | numEndTime | date | 0 |  |  | 备案编号结束时间 |
| 54 | numDetailUrl | date | 0 |  |  | 备案编号详情网址 |
| 55 | targetStr1 | varchar | 15 |  |  | 筹款标的1（别名） |
| 56 | targetStr1 | varchar | 15 |  |  | 筹款标的2（别名） |
| 57 | targetStr1 | varchar | 15 |  |  | 筹款标的2（别名） |
| 58 | targetflg | int | 1 |  | 0 | 标的别名标志（0：不使用；1：使用） |
| 59 | fundraisingMoney | decimal | 15,2 |  | 0 | 资金同步筹款金额 |
| 60 | fundraisingCount | int | 11 |  | 0 | 资金同步筹款笔数 |
| 61 | contractNumber | int | 11 |  | 0 | 月捐人数 |
| 62 | accountContractMoney | decimal | 15,2 |  | 0 | 月捐总金额 |
| 63 | amount | decimal | 15,2 | 是 | 0 | 预算总金额 |
| 64 | fundraisingStartTime | date | 0 |  |  | 筹款开始时间 |
| 65 | fundraisingEndTime | date | 0 |  |  | 筹款结束时间 |
| 66 | portraitFileId | int |  |  |  | 未成年人肖像协议文件 |
| 67 | coverage | varchar | 255 |  |  | 月捐所在地域（地域ID的组合，用,连接。业务侧新增加的月捐不允许为空） |

### 公益项目修改表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | projectId | varchar | 32 |  |  | 项目ID(PDT开头） |
| 3 | projectName | varchar | 64 |  |  | 项目名称 |
| 4 | cycle | int | 1 |  |  | 项目周期（1:1个月   2:2个月   3:3个月  6:6个月   12:12个月   99：无期限） |
| 5 | fundraisingMaxAmount | decimal | 15,2 |  |  | 筹款目标（根据项目筹款结束方式设置，可以为空） |
| 6 | projectFundStart | datetime |  |  |  | 项目筹款开始时间 |
| 7 | projectFundEnd | datetime |  |  |  | 项目筹款结束时间（根据项目筹款结束方式设置，可以为空） |
| 8 | reason | text |  |  |  | 发起缘由 |
| 9 | projectBigPicId | int | 11 |  |  | 项目主题大图（图片ID，详情页面显示） |
| 10 | projectSmallPicId | int | 11 |  |  | 项目主题小图（图片ID，首页和列表页面显示） |
| 11 | projectIntroduction | varchar | 100 |  |  | 项目简介 |
| 12 | projectTarget1 | decimal | 15,2 |  |  | 项目标的1 |
| 13 | projectTarget2 | decimal | 15,2 |  |  | 项目标的2 |
| 14 | projectTarget3 | decimal | 15,2 |  |  | 项目标的3 |
| 15 | status | int | 1 |  |  | 项目状态(0：公募修改中；1：公募提交审核；3：公募审核不通过；4：机构修改中；5：机构提交审核；6：机构审核不通过) |
| 16 | institutionId | int | 11 |  |  | 机构ID |
| 17 | holderId | int | 11 |  |  | 所属ID（填写所属活动ID或所属基金ID、所属项目ID） |
| 18 | type | int | 1 |  |  | 所属类型（0：联劝网项目；1：外部接口项目；3：基金（2018年5月平台分离前历史数据）4：母计划项目；5：子计划项目） |
| 19 | claimStatus | int | 1 |  |  | 认领状态（0：未认领；1：已认领；2：自己发起（公募基金专用）） |
| 20 | amount | decimal | 15,2 |  |  | 预算总额 |
| 21 | reviewDetail | varchar | 200 |  |  | 审核不通过的理由 |
| 22 | reviewTime | datetime |  |  |  | 审核时间 |
| 23 | reviewer | varchar | 32 |  |  | 审核者（用户名） |
| 24 | reviewNickName | varchar | 32 |  |  | 审核者昵称 |
| 25 | applyTime | datetime |  |  |  | 申请时间 |
| 26 | insertTime | datetime |  |  |  | 创建时间 |
| 27 | closeTime | datetime |  |  |  | 结束筹款时间 |
| 28 | sortNo | int | 11 |  |  | 排序（联劝网上的显示规则） |
| 29 | fundraisingAccount | varchar | 32 |  |  | 筹款账户 |
| 30 | closeReason | varchar | 200 |  |  | 结束筹款理由 |
| 31 | closer | varchar | 32 |  |  | 结束者（机构名称或管理员用户名） |
| 32 | anonymousAccount | varchar | 100 |  |  | 匿名账户 |
| 33 | ifFullStop | int | 1 |  |  | 筹满截止标志（0：不截止；1：截止） |
| 34 | webSite | varchar | 255 |  |  | 筹款网址 |
| 35 | showListFlag | int | 1 |  |  | 联劝网列表是否展示（0：不展示；1：展示） |
| 36 | showIndexFlag | int | 1 |  |  | 联劝网首页是否展示（0：不展示；1：展示） |
| 37 | mark | int | 1 |  |  | 筹款是否已结束的标志位（0：未筹款；1：筹款中；2：筹款结束；） |
| 38 | markOfEndTime | datetime |  |  |  | 筹款结束标志位更新时间（mark值更新为2时的时间） |
| 39 | coverage | varchar | 255 |  |  | 项目所在地域（地域ID的组合，用,连接。业务侧新增加的项目不允许为空） |
| 40 | ifSupportEpClaim | int | 1 |  |  | 是否支持企业认领（0：不支持；1：支持） |
| 41 | recordNumber | varchar | 255 |  |  | 备案编号 |
| 42 | createStep | int | 1 |  |  | 创建步数（1：编辑项目；2：上传图片；3：编辑详情；4：编制预算；5：收集信息；6：提交审核） |
| 43 | currentAccountType | int | 1 |  | 0 | 当前账户类型（0：未开；1：测试；2：正式） |
| 44 | fundraiseEntityGuid | varchar | 255 |  |  | 付款信息guid |
| 45 | isCollect | int | 1 |  |  | 是否收集用户信息 1:收集；2不收集 |
| 46 | lianQuanShowType | int | 1 |  |  | 在联劝网上的显示方式（0：慈善募捐；1：个人求助） |
| 47 | showIndex | int | 11 |  |  | 优先排序标记 |
| 48 | callbackurl | varchar | 255 |  |  | 回调URL，用于项目上线后通知 |
| 49 | numStartTime | date | 0 |  |  | 备案编号开始时间 |
| 50 | numEndTime | date | 0 |  |  | 备案编号结束时间 |
| 51 | numDetailUrl | date | 0 |  |  | 备案编号详情网址 |
| 52 | targetStr1 | varchar | 15 |  |  | 筹款标的1（别名） |
| 53 | targetStr1 | varchar | 15 |  |  | 筹款标的2（别名） |
| 54 | targetStr1 | varchar | 15 |  |  | 筹款标的2（别名） |
| 55 | targetflg | int | 1 |  | 0 | 标的别名标志（0：不使用；1：使用） |
| 56 | isNeedCom | int | 1 | 否 | 0 | 是否需要商品义卖（0：否；1：是） |
| 57 | logisticalAccount | varchar | 32 |  |  | 快递费账户（需要商品义买时存在） |
| 58 | fundraisingMoney | decimal | 15,2 |  | 0 | 资金同步筹款金额 |
| 59 | fundraisingCount | int | 11 |  | 0 | 资金同步筹款笔数 |
| 60 | fundMatch | decimal | 15,2 |  | 0 | 企业配捐金额 |
| 61 | countMatch | int | 11 |  | 0 | 企业配捐笔数 |
| 62 | portraitFileId | int |  |  |  | 未成年人肖像协议文件 |
| 63 | subProReviewIns | String | 32 |  |  | 子计划审核机构（母计划的创建机构） |
| 64 | projectType | int | 1 |  |  | 定制项目类型 |
| 65 | recommend | int | 1 |  |  | 是否耐克主推（1：是；0：不是） |
| 66 | matchRatio | int | 1 |  |  | 最新配捐比例（数字,通常配捐比例为1，双倍助力时为2。） |

### 日捐编辑表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号 |
| 2 | contractId | varchar | 32 | 否 |  | 日捐编号 |
| 3 | contractName | varchar | 32 | 否 |  | 日捐名称 |
| 4 | contractLogo | int | 11 | 是 |  | 主题LOGO |
| 5 | contractBigPicId | int | 11 | 是 |  | 主题图片ID，详情页面（主题图片比例为4:3） |
| 6 | contractIntroduction | text | 0 | 是 |  | 日捐简介 |
| 7 | contractTarget1 | decimal | 15 | 是 | 10 | 筹款标的1 |
| 8 | contractTarget2 | decimal | 15 | 是 | 20 | 筹款标的2 |
| 9 | contractTarget3 | decimal | 15 | 是 | 50 | 筹款标的3 |
| 10 | status | int | 1 | 否 |  | 编辑状态（0：认领机构修改中；1：认领机构提交审核；2：创建机构修改中；3：创建机构提交审核；5：认领机构审核不通过；6：创建机构审核不通过） |
| 11 | institutionId | int | 11 | 是 |  | 机构ID |
| 12 | createTime | datetime | 0 | 是 |  | 创建时间 |
| 13 | applyTime | datetime | 0 | 是 |  | 发布时间 |
| 14 | applyer | varchar | 32 | 是 |  | 发布者（用户名） |
| 15 | applyNickName | varchar | 32 | 是 |  | applyNickName |
| 16 | cancelReason | varchar | 200 | 是 |  | 撤消理由 |
| 17 | cancelTime | datetime | 0 | 是 |  | 撤消时间 |
| 18 | indexShowOrder | int | 11 | 是 |  | 首页显示顺 |
| 19 | accountcontract | varchar | 32 | 是 |  | 日捐账户 |
| 20 | fundraisingAccount | varchar | 32 | 是 |  | 筹款账户 |
| 21 | anonymousAccount | varchar | 32 | 是 |  | 匿名捐赠账户 |
| 22 | wxPlanId | varchar | 28 | 是 |  | 微信日捐模板ID |
| 23 | recordNumber | varchar | 255 | 是 |  | 备案编号 |
| 24 | lastEditStep | int | 11 | 是 |  | 上次关闭前最后修改的步骤 |
| 25 | isCollect | int | 11 | 是 |  | 是否收集用户信息 1:收集；0不收集 |
| 26 | contacts | varchar | 255 | 是 |  | 主办方联系方式/客服电话 |
| 27 | minDonationAmount | decimal | 15 | 是 |  | 起捐金额 |
| 28 | holderId | varchar | 32 | 是 |  | 所属ID（机构EntityGuid） |
| 29 | claimStatus | int | 2 | 是 |  | 认领状态（0：未认领；1：已认领；2：自己发起（公募基金专用）） |
| 30 | fundraiseEntityGuid | varchar | 32 | 是 |  | 付款信息guid |
| 31 | OnlineTime | datetime | 0 | 是 |  | 上线时间 |
| 32 | closeReason | varchar | 200 | 是 |  | 结束理由 |
| 33 | closer | varchar | 32 | 是 |  | 结束机构 |
| 34 | closeTime | datetime | 0 | 是 |  | 结束时间 |
| 35 | reviewDetail | varchar | 200 | 是 |  | 审核意见 |
| 36 | reviewTime | datetime | 0 | 是 |  | 审核时间 |
| 37 | reviewer | varchar | 32 | 是 |  | 审核者用户名 |
| 38 | reviewNickName | varchar | 32 | 是 |  | 审核者昵称 |
| 39 | lianQuanShowType | int | 1 | 是 |  | 在联劝网上的显示方式（0：慈善募捐；1：个人求助） |
| 40 | isSingleDonate | int | 2 | 是 |  | 是否需要单笔捐（1：需要；2：不需要） |
| 41 | contractAbbreviation | varchar | 12 | 是 |  | 日捐简称（发短信用） |
| 42 | showFlag | int | 2 | 是 | 0 | 是否展示 |
| 43 | score | demical | 11,2 | 是 |  | 活动/项目评分，保留一位小数 |
| 44 | sortFlag | int | 2 | 是 | 0 | 是否优先排序（新活动），0是1不是 |
| 45 | showIndex | int | 11 | 是 | 0 | 排序编号 |
| 46 | updateTime | datetime |  |  |  | 更新时间 |
| 47 | isMatch | int | 1 |  | 0 | 是否有配捐（0：否；1：是） |
| 48 | numStartTime | date | 0 |  |  | 备案编号开始时间 |
| 49 | numEndTime | date | 0 |  |  | 备案编号结束时间 |
| 50 | numDetailUrl | date | 0 |  |  | 备案编号详情网址 |
| 51 | targetStr1 | varchar | 15 |  |  | 筹款标的1（别名） |
| 52 | targetStr1 | varchar | 15 |  |  | 筹款标的2（别名） |
| 53 | targetStr1 | varchar | 15 |  |  | 筹款标的2（别名） |
| 54 | targetflg | int | 1 |  | 0 | 标的别名标志（0：不使用；1：使用） |
| 55 | fundraisingMoney | decimal | 15,2 |  | 0 | 资金同步筹款金额 |
| 56 | fundraisingCount | int | 11 |  | 0 | 资金同步筹款笔数 |
| 57 | contractNumber | int | 11 |  | 0 | 月捐人数 |
| 58 | accountContractMoney | decimal | 15,2 |  | 0 | 月捐总金额 |
| 59 | amount | decimal | 15,2 | 是 | 0 | 预算总金额 |
| 60 | fundraisingStartTime | date | 0 |  |  | 筹款开始时间 |
| 61 | fundraisingEndTime | date | 0 |  |  | 筹款结束时间 |
| 62 | portraitFileId | int |  |  |  | 未成年人肖像协议文件 |
| 63 | coverage | varchar | 255 |  |  | 日捐所在地域（地域ID的组合，用,连接。业务侧新增加的日捐不允许为空） |

### 用户表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号 |
| 2 | userId | varchar | 30 | 否 |  | 用户名 |
| 3 | userName | varchar | 20 | 是 |  | 用户真实姓名 |
| 4 | nickName | varchar | 30 | 否 |  | 用户昵称 |
| 5 | birth | datetime |  | 是 |  | 生日 |
| 6 | identityCard | varchar | 18 | 是 |  | 身份证号 |
| 7 | phone | varchar | 11 | 否 |  | 手机 |
| 8 | email | varchar | 48 | 是 |  | 邮箱 |
| 9 | pictureId | varchar | 30 | 是 |  | 头像GUID |
| 10 | donateAccount | varchar | 32 | 是 |  | 用户捐款账户（永久） |
| 11 | uid | varchar | 16 | 是 |  | 用户ID(对外公开用) |
| 12 | nationality | tinyint | 1 | 是 |  | 国籍（0：中国大陆；1：港澳及外籍人士；2：台湾同胞） |
| 13 | passport | varchar | 18 | 是 |  | 护照号 |
| 14 | sex | tinyint | 1 | 是 |  | 性别（0：男；1：女；2：性别不详） |
| 15 | nationalityDetail | varchar | 255 | 是 |  | 国籍详细 |
| 16 | creattime | varchar | 24 | 是 |  | 生成时间  YYYY-MM-dd HH:mm:ss.SSS |
| 17 | updatetime | varchar | 24 | 是 |  | 更新时间  YYYY-MM-dd HH:mm:ss.SSS |
| 18 | province | int | 11 | 是 |  | 住址的省或直辖市的地名ID |
| 19 | provinceName | varchar | 100 | 是 |  | 住址的省或直辖市的地名 |
| 20 | city | int | 11 | 是 |  | 住址的市ID |
| 21 | cityName | varchar | 100 | 是 |  | 住址的市 |
| 22 | area | int | 11 | 是 |  | 住址的区ID |
| 23 | areaName | varchar | 100 | 是 |  | 住址的区 |
| 24 | detailAddress | varchar | 255 | 是 |  | 住址的详细地址 |
| 25 | homeAddress | varchar | 255 | 是 |  | 住址 |
| 26 | workUnit | varchar | 255 | 是 |  | 工作单位 |
| 27 | address | varchar | 255 | 是 |  | 地址 |
| 28 | job | int | 11 | 是 |  | 工作 |
| 29 | postCode | varchar | 255 | 是 |  | 邮编 |
| 30 | urgentName | varchar | 255 | 是 |  |  |
| 31 | urgentPhone | varchar | 255 | 是 |  |  |
| 32 | contactEmail | varchar | 48 | 是 |  |  |
| 33 | position | varchar | 255 | 是 |  |  |
| 34 | hukouProvince | int | 11 | 是 |  | 户籍地址的省或直辖市的地名ID |
| 35 | hukouProvinceName | varchar | 100 | 是 |  | 户籍地址的省或直辖市的地名 |
| 36 | hukouCity | int | 11 | 是 |  | 户籍地址的市ID |
| 37 | hukouCityName | varchar | 100 | 是 |  | 户籍地址的市 |
| 38 | hukouAddress | varchar | 255 | 是 |  | 户籍地址的详细地址 |
| 39 | hukou | varchar | 255 | 是 |  | 户籍地址 |
| 40 | degrees | int | 1 | 是 |  | 学历（0：初中；1：高中；2：专科；3：大学本科；4：硕士；5：博士及以上） |
| 41 | tSize | int | 1 | 是 |  | T恤尺码（0：S,1：M,2：L,3：XL,4：XXL） |
| 42 | enterpriseName | varchar | 255 | 是 |  | 俱乐部或企业名称 |
| 43 | contestName | varchar | 255 | 是 |  | 比赛名称（6个月内参加） |
| 44 | event | varchar | 255 | 是 |  | 比赛项目（6个月内参加） |
| 45 | score | varchar | 255 | 是 |  | 比赛成绩（6个月内参加） |
| 46 | runMonth | decimal | 10 | 是 |  | 每个月平均跑量（单位：公里） |
| 47 | hottestYear | int | 3 | 是 |  | 平均一年参与跑步比赛场次 |
| 48 | trip | int | 1 | 是 |  | 到达本次活动地点方式（0：自驾；1：公共交通；2：其他） |
| 49 | contactPhone | varchar | 11 | 是 |  | 联系手机 |
| 50 | weixinCard | varchar | 100 | 是 |  | 微信号（歌路营为爱走一夜活动报名用） |
| 51 | campInfo | varchar | 200 | 是 |  | 特定的个人信息（歌路营为爱走一夜活动报名用，数据之间用“,”分割） |


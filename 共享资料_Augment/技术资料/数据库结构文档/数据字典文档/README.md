# 上海联劝公益基金会数据库结构文档

## 📋 文档概述

本文档集合包含了上海联劝公益基金会完整的数据库结构设计文档，经过系统性分析和整理，形成标准化的数据字典。

**分析范围**: 10个系统模块，1214个数据表，13067个字段  
**文档版本**: v1.0  
**更新日期**: 2025年7月29日  
**分析工具**: Python + pandas + openpyxl  

## 🏗️ 系统架构总览

### 核心统计数据

- **系统模块数量**: 10个
- **数据表总数**: 1214个
- **有效表数量**: 1176个（97%）
- **字段总数**: 13067个
- **平均每表字段数**: 11.1个

### 系统模块分布

| 系统名称 | 系统标识 | 表数量 | 有效表 | 字段数 | 主要功能 |
|----------|----------|--------|--------|--------|----------|
| 财务支付系统 | cello | 116 | 110 | 1,247 | 账户管理、交易处理、支付管理 |
| CRM客户关系管理系统 | crm | 17 | 16 | 210 | 客户关系维护、营销活动 |
| 联劝CRM系统 | lqscrm | 58 | 53 | 580 | 捐赠者关系、项目受益人管理 |
| 小小暴走系统 | xxbz | 52 | 51 | 616 | 活动管理、家庭团队管理 |
| 统计分析系统 | statistics | 78 | 76 | 1,024 | 数据统计、报表生成 |
| 业务管理系统 | banyan | 138 | 138 | 1,518 | 综合业务管理、工作流 |
| CAS认证系统 | cas | 17 | 16 | 210 | 统一身份认证、权限控制 |
| HORN系统 | horn | 138 | 138 | 1,518 | 专项业务管理 |
| EGG系统 | egg | 78 | 76 | 1,024 | 特定业务模块 |
| TAXUS系统 | taxus | 522 | 502 | 6,120 | 税务管理、财务合规 |

## 📊 业务模块分析

### 业务模块分布

| 业务模块 | 相关表数 | 涉及系统 | 核心功能 |
|----------|----------|----------|----------|
| 项目管理 | 121 | 6个系统 | 公益项目全生命周期管理 |
| 统计分析 | 107 | 9个系统 | 数据分析、决策支持 |
| 捐赠管理 | 78 | 9个系统 | 捐赠流程、资金管理 |
| 财务管理 | 77 | 10个系统 | 财务核算、资金监管 |
| 用户管理 | 72 | 9个系统 | 用户信息、权限管理 |
| 订单管理 | 51 | 7个系统 | 订单处理、支付流程 |
| 证书管理 | 19 | 5个系统 | 证书生成、发放管理 |

### 公益基金会特色业务

1. **多元化捐赠方式**: 支持个人捐赠、企业捐赠、月捐、日捐等多种形式
2. **透明化财务管理**: 完整的资金流水追踪和统计报表
3. **项目全程管理**: 从项目立项到执行完成的全流程管理
4. **受益人服务**: 详细的受益人信息管理和服务记录
5. **志愿者体系**: 完善的志愿者招募、培训、服务管理

## 🔗 数据关系特点

### 跨系统关联字段

| 字段名 | 出现次数 | 涉及系统 | 用途说明 |
|--------|----------|----------|----------|
| id | 1,058 | 10个系统 | 主键标识 |
| status | 280 | 10个系统 | 状态标识 |
| createTime | 252 | 8个系统 | 创建时间 |
| type | 217 | 10个系统 | 类型分类 |
| updateTime | 140 | 8个系统 | 更新时间 |
| institutionId | 116 | 6个系统 | 机构标识 |
| amount | 113 | 9个系统 | 金额字段 |
| activityId | 109 | 5个系统 | 活动标识 |

### 数据安全设计

- **用户隐私保护**: 敏感信息加密存储，访问权限严格控制
- **审计追踪**: 完整的操作日志和数据变更记录
- **数据备份**: 多层次的数据备份和恢复机制

## 📁 文档结构说明

```
数据字典文档/├── README.md                     # 总体概览和导航
├── # 整体架构和设计说明
│   └── 01-系统架构概览-系统架构总览.md
├── # 按业务功能分类的数据字典
│   ├── 02-业务模块-捐赠管理_数据字典.md
│   ├── 02-业务模块-用户管理_数据字典.md
│   ├── 02-业务模块-统计分析_数据字典.md
│   ├── 02-业务模块-订单管理_数据字典.md
│   ├── 02-业务模块-证书管理_数据字典.md
│   ├── 02-业务模块-财务管理_数据字典.md
│   └── 02-业务模块-项目管理_数据字典.md
├── # 按系统分类的详细数据字典
│   ├── 03-系统模块-CAS认证系统_数据字典.md
│   ├── 03-系统模块-CRM客户关系管理系统_数据字典.md
│   ├── 03-系统模块-EGG系统_数据字典.md
│   ├── 03-系统模块-HORN系统_数据字典.md
│   ├── 03-系统模块-TAXUS系统_数据字典.md
│   ├── 03-系统模块-业务管理系统_数据字典.md
│   ├── 03-系统模块-小小暴走系统_数据字典.md
│   ├── 03-系统模块-统计分析系统_数据字典.md
│   ├── 03-系统模块-联劝CRM系统_数据字典.md
│   └── 03-系统模块-财务支付系统_数据字典.md
├── # 表间关系和数据流图
│   └── 04-数据关系图谱-数据关系分析报告.md
├── # 技术标准和开发规范
│   └── 05-技术规范-数据库设计规范.md
```

## � 文档结构特点

### 扁平化设计优势

本文档采用**扁平化目录结构**，将所有数据字典文档直接放置在根目录下，通过文件名前缀进行分类：

- **便于快速定位**: 所有文档在同一层级，无需深入多层目录
- **清晰的分类标识**: 文件名前缀明确标识文档类别和内容
- **减少点击次数**: 避免了不必要的目录嵌套，提高访问效率
- **便于搜索**: 文件管理器和IDE中更容易搜索和定位文档

### 文件命名规范

- `01-系统架构概览-xxx.md`: 整体架构相关文档
- `02-业务模块-xxx_数据字典.md`: 业务功能模块数据字典
- `03-系统模块-xxx_数据字典.md`: 技术系统模块数据字典
- `04-数据关系图谱-xxx.md`: 数据关系分析文档
- `05-技术规范-xxx.md`: 技术标准和规范文档

## �🚀 使用指南

### 快速导航

1. **了解整体架构**: 查看 `01-系统架构概览-系统架构总览.md`
2. **按业务查找**: 浏览 `02-业务模块-xxx_数据字典.md` 对应的功能模块
3. **按系统查找**: 查看 `03-系统模块-xxx_数据字典.md` 对应的系统文档
4. **理解数据关系**: 参考 `04-数据关系图谱-数据关系分析报告.md`
5. **技术实现**: 查阅 `05-技术规范-数据库设计规范.md`

### 文档约定

- **表名**: 使用原始工作表名称，保持与设计文档一致
- **字段说明**: 包含数据类型、长度、约束条件等完整信息
- **中文注释**: 所有说明均使用中文，便于团队理解
- **版本控制**: 文档变更需要更新版本号和修改日期


---

*本文档基于上海联劝公益基金会现有数据库设计文档生成，如有疑问请联系相关技术人员。*

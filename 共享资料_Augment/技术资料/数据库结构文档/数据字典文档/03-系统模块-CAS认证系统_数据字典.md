# CAS认证系统 数据字典

## 系统概述

**系统名称**: CAS认证系统  
**系统标识**: cas  
**文件来源**: cas_dataBaseModelDesign.xls  
**工作表总数**: 17  
**有效表数量**: 16  
**字段总数**: 210  

## 表结构清单

| 序号 | 表名 | 字段数 | 说明 |
|------|------|--------|------|
| 1 | user_info | 48 |  |
| 2 | com_config | 29 |  |
| 3 | 更新履历 | 17 |  |
| 4 | access_log | 17 |  |
| 5 | user_merge_info | 15 |  |
| 6 | invoice_info | 15 |  |
| 7 | send_message | 15 |  |
| 8 | pictures | 10 |  |
| 9 | blacklist | 8 |  |
| 10 | smsstatistics | 8 |  |
| 11 | smsrecord | 7 |  |
| 12 | invoice_title_info | 6 |  |
| 13 | validate_mail | 6 |  |
| 14 | login_count | 5 |  |
| 15 | unique_key | 2 |  |
| 16 | user_info_synchro | 2 |  |

## 主要表结构详情

### user_info

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号 |
| 2 | userguid | varchar | 32 | 否 |  | 用户编号 |
| 3 | username | varchar | 32 | 否 |  | 用户名 |
| 4 | password | varchar | 64 | 是 |  | 密码 |
| 5 | nickname | varchar | 64 | 否 |  | 用户昵称 |
| 6 | phone | varchar | 11 | 是 |  | 手机 |
| 7 | mail | varchar | 50 | 是 |  | 邮箱 |
| 8 | qq | varchar | 64 | 是 |  | QQ（openid，认证登录用） |
| 9 | qqCode | varchar | 16 | 是 |  | QQ号（个人信息） |
| 10 | weibo | varchar | 64 | 是 |  | 微博（openid，认证登录用） |
| 11 | weiboCode | varchar | 48 | 是 |  | 微博（个人信息） |
| 12 | weixin | varchar | 64 | 是 |  | 微信（网站应用openid，认证登录用） |
| 13 | weixinCode | varchar | 32 | 是 |  | 微信（个人信息） |
| 14 | identitycard | varchar | 18 | 是 |  | 身份证号 |
| 15 | passportno | varchar | 32 | 是 |  | 护照编号 |
| 16 | avatarsuffix | varchar | 32 | 是 |  | 头像（大小：160*160；GridFS存储；） |
| 17 | createtime | DateTime |  |  |  | 创建时间（YYYY/MM/DD hh24:mi:ss） |
| 18 | name | varchar | 64 | 是 |  | 真实姓名 |
| 19 | formername | varchar | 64 | 是 |  | 曾用名 |
| 20 | gender | varchar | 1 | 是 | M | 性别，男：M，女：F，不详：N |
| 21 | birthday | Date |  |  |  | 出生日期（YYYY/MM/DD） |
| 22 | permission | varchar | 255 | 是 |  | 用户隐私权限信息；“字段名：权限”，采用“，”分割；不公开：0；群内公开：1；完全公开：2；手机号码为“”（默认）时，不公开；其余选项为“”（默认）时，群内公开； |
| 23 | account | varchar | 32 | 是 |  | 个人永久捐赠账户 |
| 24 | nationality | int | 2 | 是 |  | 国籍（0：中国大陆；1：港澳台及外籍人士） |
| 25 | uid | varchar | 16 | 是 |  | 个人筹款UID |
| 26 | job | int | 2 | 是 | 1 | 职业信息（0：学生；1：公司职员；2：国企事业单位职员；3：自由职业者；4：公益从业人员；5：政府机关人员；6：媒体；7：其他） |
| 27 | address | varchar | 255 | 是 |  | 通讯地址（详细地址） |
| 28 | postCode | varchar | 16 | 是 |  | 邮政编码 |
| 29 | urgentName | varchar | 64 | 是 |  | 紧急联系人姓名 |
| 30 | urgentPhone | varchar | 20 | 是 |  | 紧急联系人手机 |
| 31 | contactEmail | varchar | 50 | 是 |  | 联系邮箱 |
| 32 | unionid | varchar | 32 | 是 |  | 微信用UNIONID |
| 33 | wxnickname | varchar | 64 | 是 |  | 微信用昵称 |
| 34 | province | varchar | 255 | 是 |  | 省份 微信用 |
| 35 | city | varchar | 255 | 是 |  | 城市 微信用 |
| 36 | country | varchar | 255 | 是 |  | 国家 微信用 |
| 37 | headimgurl | varchar | 255 | 是 |  | 头像URL 微信用 |
| 38 | registerIP | varchar | 20 | 是 |  | 注册IP |
| 39 | loginIP | varchar | 20 | 是 |  | 登录IP |
| 40 | loginTime | DateTime |  |  |  | 登录时间 |
| 41 | logoutTime | DateTime |  |  |  | 退出时间 |
| 42 | originalWeixin | varchar | 64 | 是 |  | 原绑定微信（openid，认证登录用） |
| 43 | originalQq | varchar | 64 | 是 |  | 原绑定QQ（openid，认证登录用） |
| 44 | resetPwdFlag | int | 1 | 是 | 0 | 是否需要重置密码（0：不需要；1：需要） |
| 45 | useWxHeadImg | int | 1 | 是 | 1 | 是否使用微信头像（0：不使用；1：使用） |
| 46 | weixinOpenid | varchar | 64 | 是 |  | 微信（OPENID）公众号openid |
| 47 | subscribe | int | 1 | 是 | 0 | 用户是否订阅联劝公益公众号标识，值为0时，代表此用户没有关注该公众号 1：关注了 |
| 48 | workunits | varchar | 128 | 是 |  | 工作单位 |

### com_config

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号 |
| 2 | _class | varchar | 255 | 是 |  |  |
| 3 | smsSendFlg | int | 2 | 是 |  | 短信验证码发送类型 |
| 4 | smsMessage | varchar | 512 | 是 |  | 短信验证码发送信息 |
| 5 | mailTitle | varchar | 128 | 是 |  | 注册用户验证邮件主题 |
| 6 | mailMessage | text |  | 是 |  | 注册用户验证邮件内容 |
| 7 | validateMail | varchar | 128 | 是 |  | 注册用户验证邮件地址 |
| 8 | rePwdMailTitle | varchar | 128 | 是 |  | 找回密码邮件主题 |
| 9 | rePwdMailMsg | text |  | 是 |  | 找回密码邮件内容 |
| 10 | rePwdMailUrl | varchar | 128 | 是 |  | 找回密码邮件地址 |
| 11 | qqAppid | varchar | 16 | 是 |  | qq号码 |
| 12 | qqSecret | varchar | 64 | 是 |  | qq密码 |
| 13 | qqRedirectUri | varchar | 128 | 是 |  | qq重定向地址 |
| 14 | weixinAppid | varchar | 32 | 是 |  | 微信号码 |
| 15 | weixinSecret | varchar | 64 | 是 |  | 微信密码 |
| 16 | weixinRedirectUri | varchar | 128 | 是 |  | 微信重定向地址 |
| 17 | weiboAppid | varchar | 16 | 是 |  | 微博号码 |
| 18 | weiboSecret | varchar | 64 | 是 |  | 微博密码 |
| 19 | weiboRedirectUri | varchar | 128 | 是 |  | 微博重定向地址 |
| 20 | charCode | varchar | 255 | 是 |  | 非法字符 |
| 21 | banNickName | text |  | 是 |  | 敏感词 |
| 22 | CasTgtUrl | varchar | 128 | 是 |  | CAS TGT验证URL |
| 23 | CasStUrl | varchar | 128 | 是 |  | CAS ST验证URL |
| 24 | CasServiceUrl | varchar | 128 | 是 |  | CAS 服务器URL |
| 25 | toolsUrl | varchar | 128 | 是 |  | tool 服务器URL |
| 26 | encryptKey | varchar | 64 | 是 |  | 加密密钥 |
| 27 | decryptKey | varchar | 64 | 是 |  | 解密密钥 |
| 28 | blackIpList | varchar | 1000 | 是 |  | 黑名单ip（“,”隔开） |
| 29 | simplePwd | varchar | 1000 | 是 |  | 简单的密码（“,”隔开） |

### 更新履历

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | - |  |  |  |  |  |
| 2 | - |  |  |  |  |  |
| 3 | - |  |  |  |  |  |
| 4 | - |  |  |  |  |  |
| 5 | - |  |  |  |  |  |
| 6 | - |  |  |  |  |  |
| 7 | - |  |  |  |  |  |
| 8 | - |  |  |  |  |  |
| 9 | - |  |  |  |  |  |
| 10 | - |  |  |  |  |  |
| 11 | - |  |  |  |  |  |
| 12 | - |  |  |  |  |  |
| 13 | - |  |  |  |  |  |
| 14 | - |  |  |  |  |  |
| 15 | - |  |  |  |  |  |
| 16 | - |  |  |  |  |  |
| 17 | - |  |  |  |  | 微信接口获取的数据，为保证不出错而修改 |

### access_log

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号 |
| 2 | userName | varchar | 32 | 否 |  | 用户编号 |
| 3 | cookieName | varchar | 255 | 是 |  | cookie名称 |
| 4 | successFlag | int | 1 | 否 | 0 | 是否成功(0:登录成功;1:登录失败) |
| 5 | failMessage | varchar | 255 | 是 |  | 登录失败信息 |
| 6 | failNotes | varchar | 255 | 是 |  | 登录失败备注(存放明文密码) |
| 7 | ipAddress | varchar | 255 | 是 |  | 登录IP |
| 8 | systemOs | varchar | 255 | 是 |  | 操作系统 |
| 9 | systemOsVer | varchar | 255 | 是 |  | 操作系统版本号 |
| 10 | browser | varchar | 255 | 是 |  | 浏览器 |
| 11 | browserVer | varchar | 255 | 是 |  | 浏览器版本号 |
| 12 | userAgent | varchar | 512 | 是 |  | 浏览器标识（原始数据） |
| 13 | token | text |  | 是 |  | casJwtToken |
| 14 | loginTime | datetime |  | 否 |  | 登录时间 |
| 15 | logoutTime | datetime |  | 是 |  | 退出时间 |
| 16 | createtime | datetime |  | 是 |  | 记录创建时间 |
| 17 | updatetime | datetime |  | 是 |  | 记录更新时间 |

### user_merge_info

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号 |
| 2 | fromusername | varchar | 32 | 否 |  | 被合并用户名 |
| 3 | tousername | varchar | 32 | 否 |  | 用户名 |
| 4 | fromphone | varchar | 11 | 是 |  | 被合并手机 |
| 5 | tophone | varchar | 11 | 是 |  | 手机 |
| 6 | fromqq | varchar | 64 | 是 |  | 被合并QQ（openid，认证登录用） |
| 7 | toqq | varchar | 64 | 是 |  | QQ（openid，认证登录用） |
| 8 | fromweibo | varchar | 64 | 是 |  | 被合并微博（openid，认证登录用） |
| 9 | toweibo | varchar | 64 | 是 |  | 微博（openid，认证登录用） |
| 10 | fromweixin | varchar | 64 | 是 |  | 被合并微信（openid，认证登录用） |
| 11 | toweixin | varchar | 64 | 是 |  | 微信（openid，认证登录用） |
| 12 | mergeTime | DateTime |  |  |  | 创建时间（YYYY/MM/DD hh24:mi:ss） |
| 13 | password | varchar | 64 | 是 |  | 密码 |
| 14 | smsCode | varchar | 64 | 是 |  | 短信 |
| 15 | state | varchar | 16 | 是 |  | 合并类型（qq、weixin、weibo） |

### invoice_info

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号 |
| 2 | userId | int | 11 | 否 |  | 用户表ID |
| 3 | name | varchar | 64 | 是 |  | 联系人姓名 |
| 4 | workunits | varchar | 128 | 是 |  | 工作单位 |
| 5 | address | varchar | 255 | 是 |  | 邮寄地址 |
| 6 | phone | varchar | 32 | 是 |  | 联系人电话 |
| 7 | zipcode | varchar | 16 | 是 |  | 邮编 |
| 8 | province | int | 11 | 是 |  | 邮寄地址的省或直辖市的地名ID |
| 9 | city | int | 11 | 是 |  | 邮寄地址的城市id |
| 10 | fullAddress | varchar | 255 | 是 |  | 具体地址 |
| 11 | isdefault | tinyint | 2 | 是 |  | 是否是默认地址 1：是   0：否 |
| 12 | provinceName | varchar | 16 | 是 |  | 邮寄地址的省或直辖市的地名 |
| 13 | cityName | varchar | 16 | 是 |  | 邮寄地址的城市 |
| 14 | area | int | 11 | 是 |  | 邮寄地址的区id |
| 15 | areaName | varchar | 16 | 是 |  | 邮寄地址的区id |

### send_message

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号 |
| 2 | sendMsgId | varchar | 16 | 是 |  | 信息编号 |
| 3 | sendAddress | varchar | 64 | 是 |  | 发送地址 |
| 4 | sendType | int | 1 | 是 |  | 信息种类（1：手机短信 2：邮件） |
| 5 | msgTitle | varchar | 128 | 是 |  | 信息标题 |
| 6 | msgContent | text |  | 是 |  | 信息内容 |
| 7 | priLevel | int | 2 | 是 |  | 优先级 |
| 8 | sendTime | datetime |  | 是 |  | 定时 |
| 9 | sendStatus | int | 2 | 是 |  | 消息发送状态 |
| 10 | _class | varchar | 255 | 是 |  |  |
| 11 | sendCount（作废） | int | 1 | 否 | 3 | 剩余发送次数 |
| 12 | senderName（作废） | varchar | 50 | 是 |  | 发送人 |
| 13 | senderEmail（作废） | varchar | 100 | 是 |  | 发送者邮箱 |
| 14 | sendId（作废） | varchar | 32 | 是 |  | 赛邮发id |
| 15 | sendErrorMsg（作废） | varchar | 500 | 是 |  | 失败信息 |

### pictures

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号 |
| 2 | guid | varchar | 32 | 否 |  | 图片编号 |
| 3 | thumbnails | mediumblob |  | 是 |  | 缩略图 |
| 4 | icon | mediumblob |  | 是 |  | 图标 |
| 5 | originalmap | mediumblob |  | 是 |  | 原始图 |
| 6 | originalmap_name | varchar | 128 | 否 |  | 原始图名称 |
| 7 | originalmap_style | varchar | 8 | 否 |  | 原始图类型  jpg/png/bmp/jpge/gif |
| 8 | pictureupload_guid | varchar | 32 | 否 |  | 图片上传者用户ID |
| 9 | pictureupload_time | datetime |  | 否 |  | 图片上传时间（YYYY/MM/DD hh24:mi:ss） |
| 10 | isOss | int | 1 | 是 | 0 | 全局OSS标识（0:未上传 1：已上传 2：内容为空） |


# 财务支付系统 数据字典

## 系统概述

**系统名称**: 财务支付系统  
**系统标识**: cello  
**文件来源**: cello_dataBaseModelDesign.xls  
**工作表总数**: 116  
**有效表数量**: 110  
**字段总数**: 1143  

## 表结构清单

| 序号 | 表名 | 字段数 | 说明 |
|------|------|--------|------|
| 1 | 订单表 | 42 |  |
| 2 | 月捐签约表 | 36 |  |
| 3 | 日捐签约表 | 35 |  |
| 4 | 预订单表 | 33 |  |
| 5 | 成功订单表 | 33 |  |
| 6 | 公募支付配置表 | 30 |  |
| 7 | 发票表 | 29 |  |
| 8 | 订单推送表 | 27 |  |
| 9 | 线下捐赠配捐表 | 24 |  |
| 10 | 线下捐赠信息表 | 22 |  |
| 11 | 账户表 | 21 |  |
| 12 | 日捐扣款记录表 | 21 |  |
| 13 | 日捐发起扣款记录表 | 21 |  |
| 14 | 月捐扣款记录表 | 21 |  |
| 15 | 月捐发起扣款记录表 | 21 |  |
| 16 | 机构日统计表 | 20 |  |
| 17 | 合作机构每日筹款统计表 | 19 |  |
| 18 | 匿名转实名履历表 | 17 |  |
| 19 | 日捐扣款结果表 | 16 |  |
| 20 | 域名许可表 | 16 |  |
| 21 | 实体无账户外部订单表 | 15 |  |
| 22 | 520夜跑配捐 | 15 |  |
| 23 | 实际打印邮寄情报表 | 14 |  |
| 24 | 发票邮寄情报表 | 13 |  |
| 25 | 月统计表 | 13 |  |
| 26 | 接口访问日志表 | 13 |  |
| 27 | 鸡蛋暴走日统计表 | 13 |  |
| 28 | 发票申请表 | 12 |  |
| 29 | 成绩证书表 | 12 |  |
| 30 | 模板文字属性表 | 12 |  |
| 31 | 订单捐赠对象变更记录 | 12 |  |
| 32 | 月捐月度统计表 | 12 |  |
| 33 | 报名捐表 | 11 |  |
| 34 | 企业配捐表 | 11 |  |
| 35 | 资金同步统计表 | 11 |  |
| 36 | 活动项目月捐统计表 | 11 |  |
| 37 | 系统错误日志表  | 11 |  |
| 38 | 日捐日度统计表 | 10 |  |
| 39 | 月度筹款总额与新增用户统计表 | 10 |  |
| 40 | 机构每日筹款统计表 | 10 |  |
| 41 | 公募每日筹款统计表 | 10 |  |
| 42 | 合作机构筹款统计表 | 10 |  |
| 43 | 账单表 | 10 |  |
| 44 | 订单请求配置表 | 10 |  |
| 45 | 定期捐赠引流表 | 10 |  |
| 46 | 支付操作日志表 | 9 |  |
| 47 | 商品订单表 | 9 |  |
| 48 | 配捐定义表 | 9 |  |
| 49 | 资金同步统计历史表 | 9 |  |
| 50 | 公募机构统计表 | 9 |  |
| 51 | 项目捐赠耐克配捐记录表 | 9 |  |
| 52 | 报名费子订单表 | 8 |  |
| 53 | 接口域名密钥表 | 8 |  |
| 54 | 匿名实名捐月度统计表 | 8 |  |
| 55 | 支付类型月统计表 | 8 |  |
| 56 | 留言修改记录 | 8 |  |
| 57 | 退款履历表 | 8 |  |
| 58 | 鸡蛋暴走日筹款额统计表 | 8 |  |
| 59 | 捐赠项配置表 | 8 |  |
| 60 | 明细流水表 | 7 |  |
| 61 | 支付类型年月统计表 | 7 |  |
| 62 | 基金基金会机构月度收入统计表 | 7 |  |
| 63 | 成绩证书模板表 | 7 |  |
| 64 | 24小时筹款统计表 | 7 |  |
| 65 | 筹款配捐统计表 | 7 |  |
| 66 | 订单请求表 | 7 |  |
| 67 | 捐赠统计表 | 6 |  |
| 68 | 待发订单邮件信息表 | 6 |  |
| 69 | 后台通知失败表 | 6 |  |
| 70 | 发票申请周表定时生成记录  | 6 |  |
| 71 | 机构实名捐赠人数量统计表 | 6 |  |
| 72 | rabbitmq消息发送错误日志表 | 6 |  |
| 73 | 公益三小时订单关联记录表 | 6 |  |
| 74 | 配置表 | 5 |  |
| 75 | 帐户合并表 | 5 |  |
| 76 | 实体表 | 5 |  |
| 77 | 交易流水表 | 5 |  |
| 78 | 个人年度捐款统计表 | 5 |  |
| 79 | 个人年度义卖统计表 | 5 |  |
| 80 | 域名接口限流表 | 5 |  |
| 81 | 订单处理信息表 | 5 |  |
| 82 | 账单csv记录表 | 5 |  |
| 83 | 账单日结算表 | 5 |  |
| 84 | 资金同步统计操作历史表 | 5 |  |
| 85 | 日统计时间记录表 | 5 |  |
| 86 | 订单和发票申请关联表 | 4 |  |
| 87 | 日捐编号表 | 4 |  |
| 88 | 月捐编号表 | 4 |  |
| 89 | 月捐证书关联表 | 4 |  |
| 90 | 年度证书关联表 | 4 |  |
| 91 | 接口方法表 | 4 |  |
| 92 | 账单记录创建历史表 | 4 |  |
| 93 | 发票csv周记录创建历史表  | 4 |  |
| 94 | 错误信息记录表 | 4 |  |
| 95 | 帐户类型表 | 3 |  |
| 96 | 帐户状态表 | 3 |  |
| 97 | 自动转账规则表 | 3 |  |
| 98 | 实体类型表 | 3 |  |
| 99 | 交易代码表 | 3 |  |
| 100 | 订单和证书关联表 | 3 |  |
| 101 | 订单存储过程状态表 | 3 |  |
| 102 | 后台通知失败配置表 | 3 |  |
| 103 | 发票申请码表 | 3 |  |
| 104 | 定时器状态表 | 3 |  |
| 105 | 支付渠道表 | 3 |  |
| 106 | 非测试订单url配置表 | 3 |  |
| 107 | 外部订单表处理最大id表 | 2 |  |
| 108 | 24小时定义表 | 2 |  |
| 109 | 对账未成功异常订单捐赠时间统计表 | 2 |  |
| 110 | 全局自增ID表 | 1 |  |

## 主要表结构详情

### 订单表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | orderNo | varchar | 32 | 否 |  | 订单号 |
| 3 | orderName | varchar | 128 | 否 |  | 订单内容(名称) |
| 4 | entityId | varchar | 32 | 否 |  | 捐款或者报名的实体ID（可以是用户ID、队伍ID等） |
| 5 | fromAccount | varchar | 32 | 否 |  | 捐赠账户（报名账户） |
| 6 | toAccount | varchar | 255 | 否 |  | 受捐账户（活动报名费账户） |
| 7 | amount | decimal | 15,2 | 否 |  | 金额 |
| 8 | donateTime | datetime |  | 否 |  | 捐赠时间 |
| 9 | message | varchar | 1024 | 是 |  | 留言 |
| 10 | orderType | int | 1 | 否 |  | 订单种类（0：报名费 1：捐款 5：抽签费 20：报名费退款 21：捐款退款 25：抽签费退款）（2，3，4在支付系统中有使用，禁止使用） |
| 11 | orderStatus | int | 1 | 否 | 0 | 订单状态（0：未支付 1：支付成功 2：支付失败） |
| 12 | invoiceBalance | decimal | 15,2 | 否 |  | 可开发票余额 |
| 13 | phone | varchar | 11 | 是 |  | 手机号码 |
| 14 | mail | varchar | 48 | 是 |  | 邮箱地址 |
| 15 | callBackUrl | varchar | 1024 | 否 |  | 返回URL |
| 16 | autoCallBackUrl | varchar | 1024 | 否 |  | 主动回调URL |
| 17 | searchCode | varchar | 16 | 是 |  | 查询码 |
| 18 | payType | int | 1 | 否 | 0 | 支付方式（1：支付宝 2：网银（交行接口） 3：财付通 4：微信 5：银联 6：微信乐捐 8：移动和包 9：外部接口 80：企业网银（线下）81：个人网银（线下）82：财付通（线下）83：支付宝（线下 |
| 19 | accessType | int | 1 | 否 | 0 | 访问方式（0：WEB 1：MOBILE 2：管理员添加 3：外部接口） |
| 20 | tradeDetailId | bigint | 20 | 是 |  | 交易流水ID |
| 21 | invoiceStatus | int | 1 | 否 | 0 | 发票申请状态（0：未申请 1：已申请 2：已开具 3:已邮寄 4：线下已开 5：已自取 6：已退回 7：不能开票 8、9、10个人中心代码里已用 11：项目发起机构统一开票） |
| 22 | bankTradeNo | varchar | 64 | 是 |  | 银行交易流水号 |
| 23 | businessId | varchar | 32 | 是 |  | 商户系统编号（比如活动ID） |
| 24 | templetJsonData | varchar | 255 | 是 |  | 模板数据 |
| 25 | templetType | int | 1 | 是 |  | 模板类型 |
| 26 | donateType | int | 1 | 是 | 0 | 0：普通捐款 1：直接微信支付（OMP等使用） 2：有支付通道选择（OMP等使用） 3：无实际支付行为（点融网等使用）4：月捐 5：日捐 6：报名捐（作为检索条件时与orderType=1一起使用） |
| 27 | otherSiteReturnData | varchar | 255 | 是 |  | 需要记录的第三方网站返回的特殊业务数据（点融网：点融网的注册用户名） |
| 28 | sourceAccount | varchar | 32 | 是 |  | 转账源头账户（比如点融网的临时配捐总账户）、义卖商品订单号 |
| 29 | fundGuid | varchar | 32 | 是 |  | 公募基金会实体GUID |
| 30 | insGuid | varchar | 32 | 是 |  | 机构实体GUID |
| 31 | fundraiseEntityGuid | varchar | 32 | 是 |  | 支付实体id |
| 32 | checkFlag | int | 1 | 否 | 0 | 对账标记（0：未对账，1：对账成功， 2：对账未匹配, 3:退款，6:支出/提现， 7：交易失败/未支付 8：平台外数据 9：不做处理的测试数据 10:历史数据 11：线下捐款 12：报名捐 ） |
| 33 | createTime | datetime |  | 否 |  | 订单生成时间 |
| 34 | name | varchar | 32 | 否 |  | 捐赠者姓名 |
| 35 | ipAddress | varchar | 64 | 是 |  | ip地址 |
| 36 | ipType | int | 1 | 是 | 0 | ip类型（0：ipv4 1：ipv6） |
| 37 | intItem1 | int | 1 | 是 | 0 | 是否设置为爱心人士（0：未设置；1：已设置） |
| 38 | answer | varchar | 255 | 是 |  | 留言回复 |
| 39 | ansStatus | int | 11 | 是 |  | 留言回复审核状态（0：已通过；1：待审核；2：机审不通过；3：机审通过；4：审核不通过；） |
| 40 | msgStatus | int | 11 | 是 |  | 留言审核状态（0：已通过；1：待审核；2：机审不通过；3：机审通过；4：审核不通过；） |
| 41 | entryType | varchar | 255 | 是 |  | 队伍类型 |
| 42 | poolEntityId | int | 11 | 否 | 0 | 受捐所属实体ID（活动、项目、月捐、日捐实体ID） |

### 月捐签约表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | fundraiseEntityGuid | varchar | 32 | 否 |  | 支付实体GUID |
| 3 | planId | varchar | 28 | 否 |  | 模板id |
| 4 | contractCode | varchar | 32 | 否 |  | 签约协议号 |
| 5 | requestSerial | varchar | 64 | 否 |  | 请求序列号 |
| 6 | userName | varchar | 32 | 否 |  | 签约的用户ID |
| 7 | status | int | 1 | 否 | 0 | 签约状态（0：未签约 1：签约成功 2:已解约） |
| 8 | contractId | varchar | 32 | 是 |  | 委托代扣协议ID |
| 9 | operateTime | datetime |  | 否 |  | 操作时间 |
| 10 | contractTime | datetime |  | 是 |  | 签约时间 |
| 11 | contractExpiredTime | datetime |  | 是 |  | 协议到期时间 |
| 12 | contractType | int | 1 | 否 |  | 签约方式（1：微信2：支付宝 3：银联） |
| 13 | terminationType | int | 1 | 是 |  | 解约方式（0～10：微信侧解约 11：个人中心解约 12：个人中心修改月捐 13：联劝网解约 14：联劝网修改月捐 15：机构解约 16：boss管理员解约 17：连续扣款失败，自动解约  18：bo |
| 14 | terminationTime | datetime |  | 是 |  | 解约时间 |
| 15 | terminationRemark | varchar | 512 | 是 |  | 解约备注 |
| 16 | accessType | int | 1 | 否 | 0 | 访问方式（0：WEB 1：MOBILE） |
| 17 | autoCallBackUrl | varchar | 255 | 否 |  | 扣款成功异步回调URL |
| 18 | fromAccount | varchar | 32 | 否 |  | 捐赠账户（用户账户） |
| 19 | toAccount | varchar | 255 | 否 |  | 受捐账户（基金账户） |
| 20 | contractTarget | varchar | 128 | 否 |  | 月捐对象 |
| 21 | amount | decimal | 15,2 | 否 |  | 捐赠金额 |
| 22 | message | varchar | 1024 | 是 |  | 月捐留言 |
| 23 | phone | varchar | 11 | 是 |  | 手机号 |
| 24 | openid | varchar | 32 | 是 |  | 用户微信open ID |
| 25 | mail | varchar | 32 | 是 |  | 邮箱地址 |
| 26 | qq | varchar | 64 | 是 |  | QQ号 |
| 27 | name | varchar | 64 | 否 |  | 昵称 |
| 28 | payTime | datetime |  | 是 |  | 最新扣款时间 |
| 29 | payStatus | int | 1 | 是 | 0 | 扣款状态（0：等待扣款中  1：发起扣款成功 2: 发起扣款失败 3：取消扣款） |
| 30 | contractAbbreviation | varchar | 12 | 否 |  | 月捐对象简称 |
| 31 | applyCallBackUrl | varchar | 255 | 否 |  | 月捐扣款成功回调 |
| 32 | fundGuid | varchar | 32 | 是 |  | 公募基金会实体GUID |
| 33 | insGuid | varchar | 32 | 是 |  | 机构实体GUID |
| 34 | extension | varchar | 16 | 是 |  | 线下劝募渠道 |
| 35 | contractNo | varchar | 32 | 是 |  | 月捐编号 |
| 36 | skips | int | 1 | 是 | 0 | 是否跳过扣款（0：不跳过  1：跳过） |

### 日捐签约表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | fundraiseEntityGuid | varchar | 32 | 否 |  | 支付实体GUID |
| 3 | planId | varchar | 28 | 否 |  | 模板id |
| 4 | contractCode | varchar | 32 | 否 |  | 签约协议号 |
| 5 | requestSerial | varchar | 64 | 否 |  | 请求序列号 |
| 6 | userName | varchar | 32 | 否 |  | 签约的用户ID |
| 7 | status | int | 1 | 否 | 0 | 签约状态（0：未签约 1：签约成功 2:已解约） |
| 8 | contractId | varchar | 32 | 是 |  | 委托代扣协议ID |
| 9 | operateTime | datetime |  | 否 |  | 操作时间 |
| 10 | contractTime | datetime |  | 是 |  | 签约时间 |
| 11 | contractExpiredTime | datetime |  | 是 |  | 协议到期时间 |
| 12 | contractType | int | 1 | 否 |  | 签约方式（1：微信2：支付宝 3：银联） |
| 13 | terminationType | int | 1 | 是 |  | 解约方式（0～10：微信侧解约 11：个人中心解约 12：个人中心修改日捐 13：联劝网解约 14：联劝网修改日捐 15：机构解约 16：boss管理员解约 17：连续扣款失败，自动解约  18：bo |
| 14 | terminationTime | datetime |  | 是 |  | 解约时间 |
| 15 | terminationRemark | varchar | 512 | 是 |  | 解约备注 |
| 16 | accessType | int | 1 | 否 | 0 | 访问方式（0：WEB 1：MOBILE） |
| 17 | autoCallBackUrl | varchar | 255 | 否 |  | 扣款成功异步回调URL |
| 18 | fromAccount | varchar | 32 | 否 |  | 捐赠账户（用户账户） |
| 19 | toAccount | varchar | 255 | 否 |  | 受捐账户（基金账户） |
| 20 | contractTarget | varchar | 128 | 否 |  | 日捐对象 |
| 21 | amount | decimal | 15,2 | 否 |  | 捐赠金额 |
| 22 | message | varchar | 1024 | 是 |  | 日捐留言 |
| 23 | phone | varchar | 11 | 是 |  | 手机号 |
| 24 | openid | varchar | 32 | 是 |  | 用户微信open ID |
| 25 | mail | varchar | 32 | 是 |  | 邮箱地址 |
| 26 | qq | varchar | 64 | 是 |  | QQ号 |
| 27 | name | varchar | 64 | 否 |  | 昵称 |
| 28 | payTime | datetime |  | 是 |  | 最新扣款时间 |
| 29 | payStatus | int | 1 | 是 | 0 | 扣款状态（0：等待扣款中  1：发起扣款成功 2: 发起扣款失败 3：取消扣款） |
| 30 | contractAbbreviation | varchar | 12 | 否 |  | 日捐对象简称 |
| 31 | applyCallBackUrl | varchar | 255 | 否 |  | 日捐扣款成功回调 |
| 32 | fundGuid | varchar | 32 | 是 |  | 公募基金会实体GUID |
| 33 | insGuid | varchar | 32 | 是 |  | 机构实体GUID |
| 34 | extension | varchar | 16 | 是 |  | 线下劝募渠道 |
| 35 | contractNo | varchar | 32 | 是 |  | 日捐编号 |

### 预订单表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | orderNo | varchar | 32 | 否 |  | 订单号 |
| 3 | orderName | varchar | 128 | 否 |  | 订单内容(名称) |
| 4 | entityId | varchar | 32 | 否 |  | 捐款或者报名的实体ID（可以是用户ID、队伍ID等） |
| 5 | fromAccount | varchar | 32 | 否 |  | 捐赠账户（报名账户） |
| 6 | toAccount | varchar | 255 | 否 |  | 受捐账户（活动报名费账户） |
| 7 | amount | decimal | 15,2 | 否 |  | 金额 |
| 8 | donateTime | datetime |  | 否 |  | 捐赠时间 |
| 9 | message | varchar | 1024 | 是 |  | 留言 |
| 10 | orderType | int | 1 | 否 |  | 订单种类（0：报名费 1：捐款 5：抽签费 20：报名费退款 21：捐款退款 25：抽签费退款）（2，3，4在支付系统中有使用，禁止使用） |
| 11 | orderStatus | int | 1 | 否 | 0 | 订单状态（0：未支付 1：支付成功 2：支付失败） |
| 12 | invoiceBalance | decimal | 15,2 | 否 |  | 可开发票余额 |
| 13 | phone | varchar | 11 | 是 |  | 手机号码 |
| 14 | mail | varchar | 48 | 是 |  | 邮箱地址 |
| 15 | callBackUrl | varchar | 1024 | 否 |  | 返回URL |
| 16 | autoCallBackUrl | varchar | 1024 | 否 |  | 主动回调URL |
| 17 | searchCode | varchar | 16 | 是 |  | 查询码 |
| 18 | payType | int | 1 | 否 | 0 | 支付方式（1：支付宝 2：网银（交行接口） 3：财付通 4：微信 5：银联 6：微信乐捐 80：企业网银（线下）81：个人网银（线下）82：财付通（线下）83：支付宝（线下）84：现金 85：京东众筹 |
| 19 | accessType | int | 1 | 否 | 0 | 访问方式（0：WEB 1：MOBILE 2：管理员添加） |
| 20 | tradeDetailId | bigint | 20 | 是 |  | 交易流水ID |
| 21 | invoiceStatus | int | 1 | 否 | 0 | 发票申请状态（0：未申请 1：已申请 2：已开具 3:已邮寄 4：线下已开 5：已自取 6：已退回 7：不能开票 8、9、10个人中心代码里已用 11：项目发起机构统一开票） |
| 22 | bankTradeNo | varchar | 64 | 是 |  | 银行交易流水号 |
| 23 | businessId | varchar | 32 | 是 |  | 商户系统编号（比如活动ID） |
| 24 | templetJsonData | varchar | 255 | 是 |  | 模板数据 |
| 25 | templetType | int | 1 | 是 |  | 模板类型 |
| 26 | donateType | int | 1 | 是 | 0 | 0：普通捐款 1：直接微信支付（OMP等使用） 2：有支付通道选择（OMP等使用） 3：无实际支付行为（点融网等使用）4：月捐 5：义卖 6：报名捐（作为检索条件时与orderType=1一起使用） |
| 27 | otherSiteReturnData | varchar | 255 | 是 |  | 需要记录的第三方网站返回的特殊业务数据 |
| 28 | sourceAccount | varchar | 32 | 是 |  | 转账源头账户（比如点融网的临时配捐总账户）、义卖商品订单号 |
| 29 | checkFlag | int | 1 | 否 | 0 | 对账标记（0：未对账，1：对账成功， 2：对账未匹配, 3:退款，6:支出/提现， 7：交易失败/未支付 8：平台外数据 9：不做处理的测试数据 10:历史数据 11：线下捐款 12：报名捐 ） |
| 30 | createTime | datetime |  | 否 |  | 订单生成时间 |
| 31 | name | varchar | 32 | 否 |  | 捐赠者姓名 |
| 32 | ipAddress | varchar | 64 | 是 |  | ip地址 |
| 33 | ipType | int | 1 | 是 | 0 | ip类型（0：ipv4 1：ipv6） |

### 成功订单表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | orderNo | varchar | 32 | 否 |  | 订单号 |
| 3 | orderName | varchar | 128 | 否 |  | 订单内容(名称) |
| 4 | entityId | varchar | 32 | 否 |  | 捐款或者报名的实体ID（可以是用户ID、队伍ID等） |
| 5 | fromAccount | varchar | 32 | 否 |  | 捐赠账户（报名账户） |
| 6 | toAccount | varchar | 255 | 否 |  | 受捐账户（活动报名费账户） |
| 7 | amount | decimal | 15,2 | 否 |  | 金额 |
| 8 | donateTime | datetime |  | 否 |  | 捐赠时间 |
| 9 | message | varchar | 1024 | 是 |  | 留言 |
| 10 | orderType | int | 1 | 否 |  | 订单种类（0：报名费 1：捐款 5：抽签费 20：报名费退款 21：捐款退款 25：抽签费退款）（2，3，4在支付系统中有使用，禁止使用） |
| 11 | orderStatus | int | 1 | 否 | 0 | 订单状态（0：未支付 1：支付成功 2：支付失败） |
| 12 | invoiceBalance | decimal | 15,2 | 否 |  | 可开发票余额 |
| 13 | phone | varchar | 11 | 是 |  | 手机号码 |
| 14 | mail | varchar | 48 | 是 |  | 邮箱地址 |
| 15 | callBackUrl | varchar | 1024 | 否 |  | 返回URL |
| 16 | autoCallBackUrl | varchar | 1024 | 否 |  | 主动回调URL |
| 17 | searchCode | varchar | 16 | 是 |  | 查询码 |
| 18 | payType | int | 1 | 否 | 0 | 支付方式（1：支付宝 2：网银（交行接口） 3：财付通 4：微信 5：银联 6：微信乐捐 80：企业网银（线下）81：个人网银（线下）82：财付通（线下）83：支付宝（线下）84：现金 85：京东众筹 |
| 19 | accessType | int | 1 | 否 | 0 | 访问方式（0：WEB 1：MOBILE 2：管理员添加） |
| 20 | tradeDetailId | bigint | 20 | 是 |  | 交易流水ID |
| 21 | invoiceStatus | int | 1 | 否 | 0 | 发票申请状态（0：未申请 1：已申请 2：已开具 3:已邮寄 4：线下已开 5：已自取 6：已退回 7：不能开票 8、9、10个人中心代码里已用 11：项目发起机构统一开票） |
| 22 | bankTradeNo | varchar | 64 | 是 |  | 银行交易流水号 |
| 23 | businessId | varchar | 32 | 是 |  | 商户系统编号（比如活动ID） |
| 24 | templetJsonData | varchar | 255 | 是 |  | 模板数据 |
| 25 | templetType | int | 1 | 是 |  | 模板类型 |
| 26 | donateType | int | 1 | 是 | 0 | 0：普通捐款 1：直接微信支付（OMP等使用） 2：有支付通道选择（OMP等使用） 3：无实际支付行为（点融网等使用）4：月捐 5：义卖 6：报名捐（作为检索条件时与orderType=1一起使用） |
| 27 | otherSiteReturnData | varchar | 255 | 是 |  | 需要记录的第三方网站返回的特殊业务数据（点融网：点融网的注册用户名） |
| 28 | sourceAccount | varchar | 32 | 是 |  | 转账源头账户（比如点融网的临时配捐总账户）、义卖商品订单号 |
| 29 | checkFlag | int | 1 | 否 | 0 | 对账标记（0：未对账，1：对账成功， 2：对账未匹配, 3:退款，6:支出/提现， 7：交易失败/未支付 8：平台外数据 9：不做处理的测试数据 10:历史数据 11：线下捐款 12：报名捐 ） |
| 30 | createTime | datetime |  | 否 |  | 订单生成时间 |
| 31 | name | varchar | 32 | 否 |  | 捐赠者姓名 |
| 32 | ipAddress | varchar | 64 | 是 |  | ip地址 |
| 33 | ipType | int | 1 | 是 | 0 | ip类型（0：ipv4 1：ipv6） |

### 公募支付配置表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | payType | varchar | 20 | 否 |  | 支付方式 |
| 3 | partner | varchar | 32 | 否 |  | 合作身份者ID |
| 4 | subMchId | varchar | 32 | 是 |  | 子商户号 |
| 5 | key | varchar | 32 | 是 |  | 商户的私钥（签名方式MD5）——新接口不再需要该参数 |
| 6 | private_key | text |  | 是 |  | 商户的私钥（签名方式RSA） |
| 7 | ali_public_key | text |  | 是 |  | 支付宝的公钥（签名方式RSA） |
| 8 | paymentType | varchar | 1 | 否 |  | 支付类型 |
| 9 | notifyUrl | varchar | 255 | 否 |  | 服务器异步通知页面路径 |
| 10 | returnUrl | varchar | 255 | 否 |  | 页面跳转同步通知页面路径 |
| 11 | seller | varchar | 40 | 否 |  | 卖家支付宝帐户 |
| 12 | logPath | varchar | 255 | 否 |  | 日志文件夹路径 |
| 13 | inputCharset | varchar | 20 | 否 |  | 字符编码格式 |
| 14 | signType | varchar | 10 | 否 |  | 签名方式（MD5、RSA） |
| 15 | showFlg | varchar | 1 | 否 |  | 显示与否 |
| 16 | merchantUrl | varchar | 255 | 是 |  | 中断返回URL |
| 17 | decryptKey | varchar | 255 | 是 |  | 解密KEY |
| 18 | appId | varchar | 32 | 是 |  | 新版本手机网站支付应用ID |
| 19 | charset | varchar | 20 | 是 |  | 新版本手机网站支付编码格式 |
| 20 | format | varchar | 10 | 是 |  | 新版本手机网站支付参数格式 |
| 21 | privateKey | text |  | 是 |  | 新版本手机网站支付私钥 |
| 22 | pulicKey | text |  | 是 |  | 新版本手机网站支付公钥 |
| 23 | fundraiseEntityGuid | varchar | 32 | 否 |  | 支付实体id |
| 24 | ifCanInvoice | varchar | 1 | 否 |  | 是否可开票（0：不可 1：可） |
| 25 | holderCode | varchar | 6 | 是 |  | 订单号前缀 |
| 26 | appSecret | varchar | 32 | 是 |  | 微信公众号Secret，用于获取微信授权TOKEN |
| 27 | isDonate | varchar | 1 | 否 |  | 是否公益账号（0：不是 1：是） |
| 28 | start | date | 0 | 是 |  | 账单下载开始时间 |
| 29 | errorNum | int | 4 | 是 |  | 账单下载出错次数 |
| 30 | errorMsg | text | 0 | 是 |  | 账单下载出错msg |

### 发票表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | invoiceCode | varchar | 16 | 是 |  | 发票编号 |
| 3 | status | int | 1 | 是 | 0 | 发票状态（0：待开 1：正常（已开具） 2：作废） |
| 4 | printTime | datetime |  | 是 |  | 发票打印时间（发票开具时间） |
| 5 | invalidTime | datetime |  | 是 |  | 发票作废时间 |
| 6 | invalidUser | varchar | 32 | 是 |  | 发票作废操作者 |
| 7 | invoiceTitle | varchar | 128 | 是 |  | 发票抬头 |
| 8 | item1 | text |  | 是 |  | 捐赠项目1 |
| 9 | amount1 | decimal | 15,2 | 是 |  | 发票金额1 |
| 10 | item2 | varchar | 128 | 是 |  | 捐赠项目2 |
| 11 | amount2 | decimal | 15,2 | 是 |  | 发票金额2 |
| 12 | item3 | varchar | 128 | 是 |  | 捐赠项目3 |
| 13 | amount3 | decimal | 15,2 | 是 |  | 发票金额3 |
| 14 | handlePerson | varchar | 32 | 是 |  | 开票人姓名 |
| 15 | verfiedPerson | varchar | 32 | 是 |  | 复核人姓名 |
| 16 | remark | varchar | 256 | 是 |  | 备注 |
| 17 | invalidInvoiceId | int | 11 | 是 |  | 替换的作废发票ID |
| 18 | invalidReason | varchar | 255 | 是 |  | 作废理由 |
| 19 | applyTime | datetime |  | 否 |  | 发票申请时间 |
| 20 | updateTime | datetime |  | 否 |  | 发票申请信息修改时间 |
| 21 | applyCode | varchar | 8 | 否 |  | 发票申请码 |
| 22 | synchronStatus | int | 2 | 否 |  | 同步状态（0未同步，1同步完成，2同步出错） |
| 23 | message | varchar | 255 | 否 |  | 状态更新信息 |
| 24 | errorMsg | text | 0 | 是 |  | 错误原因 |
| 25 | serialNumber | varchar | 64 | 是 |  | 发票系统编号 |
| 26 | isSend | tinyint | 4 | 是 | 0 | 是否发送短信（0：未发送；1：已发送；2：无需发送） |
| 27 | invoiceImg | varchar | 500 | 是 |  | 发票图片 |
| 28 | invoicePdf | varchar | 500 | 是 |  | 发票pdf |
| 29 | shortUrl | varchar | 500 | 是 |  | 短链接地址 |

### 订单推送表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | orderId | bigint | 20 | 否 |  | 订单id |
| 3 | orderNo | varchar | 32 | 否 |  | 订单号 |
| 4 | orderName | varchar | 128 | 否 |  | 订单内容(名称) |
| 5 | amount | decimal | 15,2 | 否 |  | 金额 |
| 6 | donateTime | datetime |  | 否 |  | 捐赠时间 |
| 7 | message | varchar | 1024 | 是 |  | 留言 |
| 8 | orderType | int | 1 | 否 |  | 订单种类（0：报名费 1：捐款 5：抽签费 20：报名费退款 21：捐款退款 25：抽签费退款）（2，3，4在支付系统中有使用，禁止使用） |
| 9 | orderStatus | int | 1 | 否 | 0 | 订单状态（0：未支付 1：支付成功 2：支付失败） |
| 10 | phone | varchar | 11 | 是 |  | 手机号码 |
| 11 | mail | varchar | 48 | 是 |  | 邮箱地址 |
| 12 | searchCode | varchar | 16 | 是 |  | 查询码 |
| 13 | payType | int | 1 | 否 | 0 | 支付方式（1：支付宝 2：网银（交行接口） 3：财付通 4：微信 5：银联 6：微信乐捐 8：移动和包 9：外部接口 80：企业网银（线下）81：个人网银（线下）82：财付通（线下）83：支付宝（线下 |
| 14 | accessType | int | 1 | 否 | 0 | 访问方式（0：WEB 1：MOBILE 2：管理员添加 3：外部接口） |
| 15 | bankTradeNo | varchar | 64 | 是 |  | 银行交易流水号 |
| 16 | donateType | int | 1 | 是 | 0 | 0：普通捐款 1：直接微信支付（OMP等使用） 2：有支付通道选择（OMP等使用） 3：无实际支付行为（点融网等使用）4：月捐 5：义卖 6：报名捐（作为检索条件时与orderType=1一起使用） |
| 17 | sourceAccount | varchar | 32 | 是 |  | 转账源头账户（比如点融网的临时配捐总账户）、义卖商品订单号 |
| 18 | name | varchar | 32 | 否 |  | 捐赠者姓名 |
| 19 | entityId | varchar | 32 | 否 |  | 捐款或者报名的实体ID（可以是用户ID、队伍ID等） |
| 20 | fromAccount | varchar | 32 | 否 |  | 捐赠账户（报名账户） |
| 21 | attach | varchar | 255 | 是 |  | 回传参数，如果请求时传递了该参数，则返回给第三方时会原样回传该参数。 |
| 22 | fundGuid | varchar | 32 | 否 |  | 基金会实体GUID |
| 23 | sendStatus | int | 2 | 否 | 0 | 推送状态：0：未推送 1：成功推送 2：推送失败 |
| 24 | projectGuid | varchar | 32 | 否 |  | 项目Guid |
| 25 | retryTimes | int | 2 | 是 |  | 剩余重试次数 |
| 26 | executeDate | datetime |  | 是 |  | 上次执行时间 |
| 27 | sendErrMessage | longtext |  | 是 |  | 错误信息（追加每次的错误信息） |

### 线下捐赠配捐表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | fromEntityGuid | varchar | 32 | 否 |  | 捐赠人guid |
| 3 | fromEntityName | varchar | 64 | 否 |  | 捐赠者姓名 |
| 4 | fromAccount | varchar | 32 | 否 |  | 捐赠者账户 |
| 5 | status | int | 4 | 否 |  | 状态（0：未确认；1：已确认；2：捐赠成功；3：捐赠失败） |
| 6 | amount | decimal | 15，2 | 否 |  | 捐赠金额 |
| 7 | toEntityGuid | varchar | 32 | 否 |  | 受捐者guid |
| 8 | toEntityType | int | 4 | 否 |  | 受捐者类型（0：活动；1：项目；2：月捐；3：无需报名；4：个人报名；5：组队报名；6：爱心回馈；7：个人报名下的个人；8：组队报名下的个人；9：组队报名下的队伍；10：小小暴走家庭；11：小小暴走活 |
| 9 | toEntityName | varchar | 64 | 否 |  | 受捐者名称 |
| 10 | toAccount | varchar | 32 | 否 |  | 受捐者账户 |
| 11 | toObjName | varchar | 64 | 否 |  | 活动项目月捐名称 |
| 12 | toObjGuid | varchar | 32 | 否 |  | 活动/项目/月捐实体id |
| 13 | toFundGuid | varchar | 32 | 否 |  | 公募基金会实体GUID |
| 14 | toInsGuid | varchar | 32 | 否 |  | 机构实体GUID |
| 15 | orderType | int | 4 | 否 |  | 订单类别（1：捐赠） |
| 16 | payType | int | 4 | 否 |  | 支付方式（80：银行；84：现金） |
| 17 | donateTime | datetime | 0 | 否 |  | 捐赠时间 |
| 18 | message | varchar | 1024 | 否 |  | 留言 |
| 19 | orderId | int | 11 |  |  | 订单表id |
| 20 | applyTime | datetime | 0 |  |  | 确认捐赠时间 |
| 21 | oiId | bigint | 20 | 否 |  | 线下捐赠id（taxus） |
| 22 | personalId | int | 11 |  |  | 用户个人报名id |
| 23 | personalType | int | 1 |  |  | 筹款目标方式 |
| 24 | fundraiseEntityGuid | varchar | 32 |  |  | 支付实体GUID |

### 线下捐赠信息表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | fromEntityGuid | varchar | 32 | 否 |  | 捐赠人guid |
| 3 | fromEntityName | varchar | 64 | 否 |  | 捐赠者姓名 |
| 4 | fromAccount | varchar | 32 | 否 |  | 捐赠者账户 |
| 5 | status | int | 4 | 否 |  | 状态（0：待确认；1：已确认） |
| 6 | amount | decimal | 15，2 | 否 |  | 捐赠金额 |
| 7 | toEntityGuid | varchar | 32 | 否 |  | 受捐者guid |
| 8 | toEntityType | int | 4 | 否 |  | 受捐者类型（0：活动；1：项目；2：月捐；3：无需报名；4：个人报名；5：组队报名；6：爱心回馈；7：个人报名下的个人；8：组队报名下的个人；9：组队报名下的队伍；） |
| 9 | toEntityName | varchar | 64 | 否 |  | 受捐者名称 |
| 10 | toAccount | varchar | 32 | 否 |  | 受捐者账户 |
| 11 | toObjName | varchar | 64 | 否 |  | 活动项目月捐名称 |
| 12 | toObjGuid | varchar | 32 | 否 |  | 活动/项目/月捐实体id |
| 13 | toFundGuid | varchar | 32 | 否 |  | 公募基金会实体GUID |
| 14 | toInsGuid | varchar | 32 | 否 |  | 机构实体GUID |
| 15 | orderType | int | 4 | 否 |  | 订单类别（1：捐赠） |
| 16 | payType | int | 4 | 否 |  | 支付方式（80：银行；84：现金） |
| 17 | donateTime | datetime | 0 | 否 |  | 捐赠时间 |
| 18 | message | varchar | 1024 | 否 |  | 留言 |
| 19 | fileId | int | 11 | 否 |  | 凭证图片id |
| 20 | orderId | int | 11 |  |  | 订单表id |
| 21 | applyTime | datetime | 0 |  |  | 确认捐赠时间 |
| 22 | fundraiseEntityGuid | varchar | 32 |  |  | 支付实体GUID |


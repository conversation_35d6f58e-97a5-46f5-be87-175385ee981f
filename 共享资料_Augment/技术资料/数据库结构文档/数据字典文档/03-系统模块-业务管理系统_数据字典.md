# 业务管理系统 数据字典

## 系统概述

**系统名称**: 业务管理系统  
**系统标识**: banyan  
**文件来源**: banyan_dataBaseModelDesign.xls  
**工作表总数**: 287  
**有效表数量**: 281  
**字段总数**: 2182  

## 表结构清单

| 序号 | 表名 | 字段数 | 说明 |
|------|------|--------|------|
| 1 | 公益项目表 | 92 |  |
| 2 | 公益活动表 | 72 |  |
| 3 | 公益基金表 | 52 |  |
| 4 | 项目实行表 | 47 |  |
| 5 | 专项合作表 | 46 |  |
| 6 | 函数目录 | 45 |  |
| 7 | 活动报销申请表 | 32 |  |
| 8 | 资金拨付表 | 31 |  |
| 9 | 新闻表 | 31 |  |
| 10 | 公益账房票据申请信息表 | 31 |  |
| 11 | 合同管理表 | 30 |  |
| 12 | 拨付申请表 | 28 |  |
| 13 | 公益宝贝账房开票申请信息表 | 28 |  |
| 14 | 线下退款表 | 24 |  |
| 15 | 机构表 | 22 |  |
| 16 | 行政预算表 | 21 |  |
| 17 | 机构登记信息表 | 21 |  |
| 18 | 联劝官网视频表 | 21 |  |
| 19 | wf_workitem | 21 |  |
| 20 | 行政报销表 | 20 |  |
| 21 | 企业表 | 20 |  |
| 22 | 阶段性报告表 | 20 |  |
| 23 | NGO项目表 | 20 |  |
| 24 | 商户用户表 | 20 |  |
| 25 | 域名申请表 | 19 |  |
| 26 | 资助项目临时表 | 19 |  |
| 27 | 支付宝原始账单模板表 | 19 |  |
| 28 | 支付宝合并开票申请表 | 19 |  |
| 29 | 机构自助用户表 | 18 |  |
| 30 | 批量支付确认详情表 | 18 |  |
| 31 | 机构管理员表 | 17 |  |
| 32 | 业务消息通知表 | 17 |  |
| 33 | 项目资料表 | 17 |  |
| 34 | 拖拽控件表 | 16 |  |
| 35 | 机构介绍表 | 16 |  |
| 36 | 联劝月报表 | 16 |  |
| 37 | 项目执行反馈表 | 16 |  |
| 38 | 项目执行状况表 | 16 |  |
| 39 | 提款信息收集表 | 16 |  |
| 40 | 机构运营用户表 | 16 |  |
| 41 | 商户主体表 | 16 |  |
| 42 | 费用预算表 | 15 |  |
| 43 | 资金实际拨付表 | 15 |  |
| 44 | NGO项目反馈表 | 15 |  |
| 45 | 证书模板文字属性表 | 14 |  |
| 46 | 新版操作日志表  | 14 |  |
| 47 | 任务历史表 | 14 |  |
| 48 | 任务表 | 14 |  |
| 49 | 公益账房票据申请获取记录表 | 14 |  |
| 50 | 行政报销清单表 | 13 |  |
| 51 | 新版错误日志表 | 13 |  |
| 52 | 项目拨付表 | 13 |  |
| 53 | 项目执行预算表 | 13 |  |
| 54 | 月度信息反馈表 | 13 |  |
| 55 | 联劝团队表 | 13 |  |
| 56 | 模块表 | 13 |  |
| 57 | 流程实例表 | 13 |  |
| 58 | 联劝官网的征集公告表 | 12 |  |
| 59 | 其他费用支出表 | 12 |  |
| 60 | 项目表 | 12 |  |
| 61 | 模板表 | 12 |  |
| 62 | 流程定义表 | 12 |  |
| 63 | NGO项目预算表 | 12 |  |
| 64 | 活动报销清单表 | 11 |  |
| 65 | 联劝官网轮播图管理 | 11 |  |
| 66 | 项目审核表 | 11 |  |
| 67 | 项目经验表 | 11 |  |
| 68 | 项目计划表 | 11 |  |
| 69 | 项目执行反馈文件表 | 11 |  |
| 70 | 流程实例历史表 | 11 |  |
| 71 | NGO项目其他费用支出表 | 11 |  |
| 72 | 联劝官网项目案例表 | 10 |  |
| 73 | 联劝官网的善款使用报告表 | 10 |  |
| 74 | 项目申报平台表 | 10 |  |
| 75 | 地区表 | 10 |  |
| 76 | 机构团队成员表 | 10 |  |
| 77 | 信息披露素材表 | 10 |  |
| 78 | 线下项目表  | 10 |  |
| 79 | 联劝官网合作伙伴 | 10 |  |
| 80 | 项目执行表 | 10 |  |
| 81 | 模板答案表 | 10 |  |
| 82 | 同类项目表 | 10 |  |
| 83 | 票据文件表 | 10 |  |
| 84 | 导出文件记录表 | 10 |  |
| 85 | 公益宝贝账房开票申请结果表 | 10 |  |
| 86 | 公益账房票据申请结果表 | 10 |  |
| 87 | 企业参与合作伙伴logo表 | 10 |  |
| 88 | 企业参与轮播图表 | 10 |  |
| 89 | 公益宝贝微信账单临时表 | 10 |  |
| 90 | 项目申请计划书表 | 9 |  |
| 91 | 证书模板表 | 9 |  |
| 92 | 合作机构表 | 9 |  |
| 93 | 联劝新闻表 | 9 |  |
| 94 | 项目管理费用支出表 | 9 |  |
| 95 | 资助项目反馈审核信息表 | 9 |  |
| 96 | 模块图片表 | 9 |  |
| 97 | 财务批量支付确认表 | 9 |  |
| 98 | 公益活动表-公众参与 | 9 |  |
| 99 | 机构银行账户表 | 8 |  |
| 100 | 机构基本信息表 | 8 |  |
| 101 | 部门表 | 8 |  |
| 102 | 申报平台模板表 | 8 |  |
| 103 | 财务报表表 | 8 |  |
| 104 | 专项基金联劝官网设置表 | 8 |  |
| 105 | 联劝荣誉表 | 8 |  |
| 106 | 机构形态表 | 8 |  |
| 107 | 素材表 | 8 |  |
| 108 | 业务消息通知详细表 | 8 |  |
| 109 | 系统操作记录表 | 8 |  |
| 110 | 项目执行团队成员表 | 8 |  |
| 111 | 项目目标表 | 8 |  |
| 112 | 用户银行信息表 | 8 |  |
| 113 | 委托代理表 | 8 |  |
| 114 | 线下捐赠操作记录表 | 8 |  |
| 115 | NGO项目管理费用支出表 | 8 |  |
| 116 | 公益宝贝微信账单月统计表 | 8 |  |
| 117 | 地区统计表 | 7 |  |
| 118 | 证书图片表 | 7 |  |
| 119 | 合同管理操作记录表 | 7 |  |
| 120 | 大事件表 | 7 |  |
| 121 | 公益基金合作详情表 | 7 |  |
| 122 | 模板答案图片表 | 7 |  |
| 123 | 资助类型表 | 7 |  |
| 124 | 行政管理费比例表 | 6 |  |
| 125 | 拨付预算附件表 | 6 |  |
| 126 | 拨付反馈附件表 | 6 |  |
| 127 | 报销类型表 | 6 |  |
| 128 | 筹款预算表 | 6 |  |
| 129 | 资助机构设置表 | 6 |  |
| 130 | 资金池比例选中表 | 6 |  |
| 131 | 新闻详情表 | 6 |  |
| 132 | 项目实施地点表 | 6 |  |
| 133 | 风险预案表 | 6 |  |
| 134 | 联劝团队类型表 | 6 |  |
| 135 | 抄送实例表 | 6 |  |
| 136 | OSS文件表 | 6 |  |
| 137 | NGO项目计划表 | 6 |  |
| 138 | NGO项目拨付记录表 | 6 |  |
| 139 | 业务类型业务员关联表 | 5 |  |
| 140 | 文件表 | 5 |  |
| 141 | 项目执行省份表 | 5 |  |
| 142 | 项目筹款信息表 | 5 |  |
| 143 | 标签用户表 | 5 |  |
| 144 | 标签密钥表 | 5 |  |
| 145 | 新闻文件表 | 5 |  |
| 146 | 反馈提醒表 | 5 |  |
| 147 | 项目合作机构表 | 5 |  |
| 148 | 资助项目临时地域表 | 5 |  |
| 149 | 短信发送记录表 | 5 |  |
| 150 | 基础配置信息表 | 5 |  |
| 151 | 页面表 | 5 |  |
| 152 | 权限表 | 5 |  |
| 153 | OA报销票据表 | 5 |  |
| 154 | 拨付预算附件关系表 | 4 |  |
| 155 | 拨付反馈附件关系表 | 4 |  |
| 156 | 新闻频道表 | 4 |  |
| 157 | 合同类别表 | 4 |  |
| 158 | 项目执行图片表 | 4 |  |
| 159 | 错误信息记录表 | 4 |  |
| 160 | 机构性质表 | 4 |  |
| 161 | 新闻频道关联表 | 4 |  |
| 162 | 新闻附件表 | 4 |  |
| 163 | 月度信息反馈图片关系表 | 4 |  |
| 164 | 项目产出表 | 4 |  |
| 165 | 项目发布平台表 | 4 |  |
| 166 | 报销附件表 | 4 |  |
| 167 | 企业参与合作方式表 | 4 |  |
| 168 | 合同到期通知用户表 | 3 |  |
| 169 | 部门成员关系表 | 3 |  |
| 170 | 平台资金池表 | 3 |  |
| 171 | 存储过程定时器开关表 | 3 |  |
| 172 | 地域表 | 3 |  |
| 173 | 权限组对应权限表 | 3 |  |
| 174 | 管理员所属权限组表 | 3 |  |
| 175 | 页面权限组表 | 3 |  |
| 176 | 官网资助类型表 | 3 |  |
| 177 | 枢纽NGO机构关联表 | 3 |  |
| 178 | 商户附件表 | 3 |  |
| 179 | 权限组表 | 2 |  |
| 180 | 历史任务参与者表 | 2 |  |
| 181 | 任务参与者表 | 2 |  |
| 182 | 拨付流水号表 | 1 |  |
| 183 | 关键字表 | 1 |  |
| 184 | temp_fund_coo_field | 1 |  |
| 185 | 证书模板表2 | 0 |  |
| 186 | 移动文件临时表 | 0 |  |
| 187 | 管理员帐号表 | 0 |  |
| 188 | 活动商品关系表 | 0 |  |
| 189 | 首页轮播图管理表 | 0 |  |
| 190 | 活动项目标签表 | 0 |  |
| 191 | 公益活动详情表 | 0 |  |
| 192 | 活动队伍关联表 | 0 |  |
| 193 | 机构审核表 | 0 |  |
| 194 | 平台升级公告表 | 0 |  |
| 195 | 机构年度报告表 | 0 |  |
| 196 | 附件图片表 | 0 |  |
| 197 | 首页轮播活动设置表 | 0 |  |
| 198 | 义卖自定义设置表 | 0 |  |
| 199 | 义卖快递费设置表 | 0 |  |
| 200 | 义卖地域表 | 0 |  |
| 201 | 机构负责人表 | 0 |  |
| 202 | 慈善组织机构表 | 0 |  |
| 203 | 慈善组织机构的募捐方案表 | 0 |  |
| 204 | 义卖商品表 | 0 |  |
| 205 | 商品物流表 | 0 |  |
| 206 | 商品从订单表  | 0 |  |
| 207 | 联劝网投诉建议表 | 0 |  |
| 208 | 月捐设置表 | 0 |  |
| 209 | 月捐模板表 | 0 |  |
| 210 | 月捐模板图片表 | 0 |  |
| 211 | 月捐用户海报表 | 0 |  |
| 212 | crm标签表 | 0 |  |
| 213 | 机构注销表 | 0 |  |
| 214 | 域名黑白名单表 | 0 |  |
| 215 | 捐赠配置定义表 | 0 |  |
| 216 | 捐赠信息收集表 | 0 |  |
| 217 | 企业项目表 | 0 |  |
| 218 | 错误信息表 | 0 |  |
| 219 | 领域表 | 0 |  |
| 220 | 领域关系表 | 0 |  |
| 221 | 项目企业认领表 | 0 |  |
| 222 | 公益项目筹款预算表 | 0 |  |
| 223 | 公益项目详情表 | 0 |  |
| 224 | 公益项目进展表 | 0 |  |
| 225 | 公益项目进展详情表 | 0 |  |
| 226 | 筹款箱表 | 0 |  |
| 227 | 筹款箱模板表 | 0 |  |
| 228 | 共通报表表 | 0 |  |
| 229 | 站内信表 | 0 |  |
| 230 | 联合捐表 | 0 |  |
| 231 | 标签表 | 0 |  |
| 232 | 标签分类表 | 0 |  |
| 233 | 标签颜色表 | 0 |  |
| 234 | 操作日志表 | 0 |  |
| 235 | 爱心传递表 | 0 |  |
| 236 | 爱心传递实名支付记录表 | 0 |  |
| 237 | 联劝网留言评价表 | 0 |  |
| 238 | 留言反馈表 | 0 |  |
| 239 | 联劝网留言回复表 | 0 |  |
| 240 | 业务消息通知功能项表 | 0 |  |
| 241 | 业务消息通知功能项权限关系表 | 0 |  |
| 242 | 订单特殊化处理表 | 0 |  |
| 243 | 商品主订单表 | 0 |  |
| 244 | 商品预订单表 | 0 |  |
| 245 | 观点表 | 0 |  |
| 246 | 项目统计表 | 0 |  |
| 247 | 项目推荐设置表 | 0 |  |
| 248 | 项目分值表 | 0 |  |
| 249 | 项目筹款标的表 | 0 |  |
| 250 | 推广表 | 0 |  |
| 251 | 推广访问记录表 | 0 |  |
| 252 | 推广渠道表 | 0 |  |
| 253 | 推广渠道类型表 | 0 |  |
| 254 | 公募基金表 | 0 |  |
| 255 | 页面请求对照表 | 0 |  |
| 256 | 页面请求信息表 | 0 |  |
| 257 | 新闻请求日统计表 | 0 |  |
| 258 | 新闻请求月统计表 | 0 |  |
| 259 | 页面请求日统计表 | 0 |  |
| 260 | 页面请求月统计表 | 0 |  |
| 261 | 页面检索请求信息统计表 | 0 |  |
| 262 | 页面检索条件基础表 | 0 |  |
| 263 | 页面请求统计表 | 0 |  |
| 264 | 月捐审核表 | 0 |  |
| 265 | 业务审核表 | 0 |  |
| 266 | 公益系列表 | 0 |  |
| 267 | 公益系列活动项目表 | 0 |  |
| 268 | 维护反馈表 | 0 |  |
| 269 | 维护记录表 | 0 |  |
| 270 | 月捐短信模板表 | 0 |  |
| 271 | 临时赠品物流表 | 0 |  |
| 272 | 定时任务策略表 | 0 |  |
| 273 | 用户标记表 | 0 |  |
| 274 | 联劝网引导页设置表 | 0 |  |
| 275 | 义卖订单存储过程标记表 | 0 |  |
| 276 | 鸡蛋暴走用户表 | 0 |  |
| 277 | t_xugj_file | 0 |  |
| 278 | t_xugj_no_file | 0 |  |
| 279 | temp_ins_info | 0 |  |
| 280 | toolkitSet | 0 |  |
| 281 | user_institution_contrast | 0 |  |

## 主要表结构详情

### 公益项目表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | projectId | varchar | 32 |  |  | 项目ID(PDT开头） |
| 3 | projectName | varchar | 64 | 否 |  | 项目名称 |
| 4 | projectFields | varchar | 32 |  |  | 项目领域（只要一级领域） |
| 5 | cycle | int | 1 |  |  | 项目周期（1:1个月   2:2个月   3:3个月  6:6个月   12:12个月   99：无期限） |
| 6 | fundraisingMaxAmount | decimal | 15,2 |  |  | 筹款目标（根据项目筹款结束方式设置，可以为空） |
| 7 | projectFundEnd | datetime |  |  |  | 项目筹款结束时间（根据项目筹款结束方式设置，可以为空） |
| 8 | reason | text |  |  |  | 发起缘由 |
| 9 | projectBigPicId | int | 11 |  |  | 项目主题大图（图片ID，详情页面显示） |
| 10 | projectSmallPicId | int | 11 |  |  | 项目主题小图（图片ID，首页和列表页面显示） |
| 11 | projectIntroduction | varchar | 100 |  |  | 项目简介 |
| 12 | projectTarget1 | decimal | 15,2 |  | 20 | 项目标的1 |
| 13 | projectTarget2 | decimal | 15,2 |  | 50 | 项目标的2 |
| 14 | projectTarget3 | decimal | 15,2 |  | 100 | 项目标的3 |
| 15 | keywords1 | int | 11 |  |  | 关键字1 |
| 16 | keywords2 | int | 11 |  |  | 关键字2 |
| 17 | keywords3 | int | 11 |  |  | 关键字3 |
| 18 | donatedGpId | varchar | 32 |  |  | 受捐群体名称 |
| 19 | donatedGpName | varchar | 20 |  |  | 受捐群体负责人姓名 |
| 20 | donatedGpTel | varchar | 20 |  |  | 受捐群体联系电话 |
| 21 | donatedGpAddress | varchar | 100 |  |  | 受捐群体地址 |
| 22 | status | int | 1 | 否 | 0 | 项目状态（0：草稿；1：审核中；2：进行中（募集中或募集结束）；3：审核不通过；4：已关闭；5:第三方平台） |
| 23 | institutionId | int | 11 | 否 |  | 机构ID |
| 24 | holderId | int | 11 |  |  | 所属ID（填写所属活动ID或所属基金ID、所属项目ID） |
| 25 | type | int | 1 | 否 | 0 | 所属类型（1：独立项目；2：基金；3：合作 ；5：公募基金…） |
| 26 | detailPic1 | int | 11 |  |  | 详情图片1（图片ID） |
| 27 | detailTitle1 | varchar | 50 |  |  | 详情标题1 |
| 28 | detailInfo1 | varchar | 100 |  |  | 详情描述1 |
| 29 | detailPic2 | int | 11 |  |  | 详情图片2（图片ID） |
| 30 | detailTitle2 | varchar | 50 |  |  | 详情标题2 |
| 31 | detailInfo2 | varchar | 100 |  |  | 详情描述2 |
| 32 | detailPic3 | int | 11 |  |  | 详情图片3（图片ID） |
| 33 | detailTitle3 | varchar | 50 |  |  | 详情标题3 |
| 34 | detailInfo3 | varchar | 100 |  |  | 详情描述3 |
| 35 | detailPic4 | int | 11 |  |  | 详情图片4（图片ID） |
| 36 | detailTitle4 | varchar | 50 |  |  | 详情标题4 |
| 37 | detailInfo4 | varchar | 100 |  |  | 详情描述4 |
| 38 | detailPic5 | int | 11 |  |  | 详情图片5（图片ID） |
| 39 | detailTitle5 | varchar | 50 |  |  | 详情标题5 |
| 40 | detailInfo5 | varchar | 100 |  |  | 详情描述5 |
| 41 | detailPic6 | int | 11 |  |  | 详情图片6（图片ID） |
| 42 | detailTitle6 | varchar | 50 |  |  | 详情标题1 |
| 43 | detailInfo6 | varchar | 100 |  |  | 详情描述6 |
| 44 | detailPic7 | int | 11 |  |  | 详情图片7（图片ID） |
| 45 | detailTitle7 | varchar | 50 |  |  | 详情标题7 |
| 46 | detailInfo7 | varchar | 100 |  |  | 详情描述7 |
| 47 | detailPic8 | int | 11 |  |  | 详情图片8（图片ID） |
| 48 | detailTitle8 | varchar | 50 |  |  | 详情标题8 |
| 49 | detailInfo8 | varchar | 100 |  |  | 详情描述8 |
| 50 | detailPic9 | int | 11 |  |  | 详情图片9（图片ID） |
| 51 | detailTitle9 | varchar | 50 |  |  | 详情标题9 |
| 52 | detailInfo9 | varchar | 100 |  |  | 详情描述9 |
| 53 | detailPic10 | int | 11 |  |  | 详情图片10（图片ID） |
| 54 | detailTitle10 | varchar | 50 |  |  | 详情标题10 |
| 55 | detailInfo10 | varchar | 100 |  |  | 详情描述10 |
| 56 | amount | decimal | 15,2 |  |  | 预算总额 |
| 57 | reviewDetail | varchar | 200 |  |  | 审核不通过的理由 |
| 58 | reviewTime | datetime |  |  |  | 审核时间 |
| 59 | reviewer | varchar | 32 |  |  | 审核者（用户名） |
| 60 | reviewNickName | varchar | 32 |  |  | 审核者昵称 |
| 61 | applyTime | datetime |  |  |  | 申请时间 |
| 62 | insertTime | datetime |  | 否 |  | 创建时间 |
| 63 | closeTime | datetime |  |  |  | 结束筹款时间 |
| 64 | sortNo | int | 11 |  |  | 排序（联劝网上的显示规则） |
| 65 | fundraisingAccount | varchar | 32 |  |  | 筹款账户 |
| 66 | closeReason | varchar | 200 |  |  | 结束筹款理由 |
| 67 | closer | varchar | 32 |  |  | 结束者（机构名称或管理员用户名） |
| 68 | snakerOrderId | varchar | 32 |  |  | 流程实例ID |
| 69 | snakerStepName | varchar | 100 |  |  | 当前任务名 |
| 70 | snakerStatus | varchar | 100 |  |  | 当前状态 |
| 71 | anonymousAccount | varchar | 100 |  |  | 匿名账户 |
| 72 | adminFeeType | tinyint | 1 |  |  | 行政管理费提取类型（0：不提取 1：固定比例提取 2：累进金额按约定比例提取 ） |
| 73 | adminFeeFixedRate | decimal | 5,2 |  |  | 行政管理费固定提取比例(非固定比例为空) |
| 74 | publishPlatformId | int | 11 |  |  | 项目发布平台ID(为空时，联劝网线上发布) |
| 75 | pointTo | int | 1 | 否 |  | 用款指向（1：专款专用；2：无指向性） |
| 76 | ifFullStop | int | 1 | 否 | 0 | 筹满截止标志（0：不截止；1：截止） |
| 77 | webSite | varchar | 255 |  |  | 筹款网址 |
| 78 | firstProjectName | varchar | 64 |  |  | 第一次录入的项目名称 |
| 79 | showListFlag | int | 1 | 否 | 0 | 联劝网列表是否展示（0：不展示；1：展示） |
| 80 | showIndexFlag | int | 1 | 否 | 0 | 联劝网首页是否展示（0：不展示；1：展示） |
| 81 | cycleEndTime | datetime |  |  |  | 项目最终结束时间/周期用户反馈邮件任务生成时间 |
| 82 | endFeedbackMark | int | 1 | 否 | 0 | 项目最终结束用户反馈标志（0：未生成反馈邮件；1：已生成反馈邮件；2：历史数据不用反馈） |
| 83 | mark | int | 1 | 否 | 0 | 筹款是否已结束的标志位（0：未筹款；1：筹款中；2：筹款结束；） |
| 84 | markOfEndTime | datetime |  |  |  | 筹款结束标志位更新时间（mark值更新为2时的时间） |
| 85 | ifNeedPhoneAM | int | 1 | 否 | 0 | 匿名捐赠是否必填手机（0：非必填；1：必填） |
| 86 | ifNeedEmailAM | int | 1 | 否 | 0 | 匿名捐赠是否必填邮箱（0：非必填；1：必填） |
| 87 | coverage | varchar | 32 |  |  | 项目所在地域（地域ID的组合，用,连接。业务侧新增加的项目不允许为空） |
| 88 | ifSupportEpClaim | int | 1 | 否 | 0 | 是否支持企业认领（0：不支持；1：支持） |
| 89 | previewCode | varchar | 32 |  |  | 预览校验码 |
| 90 | recordNumber | varchar | 255 |  |  | 备案编号 |
| 91 | historyDB | int | 4 |  | 0 | 第三方平台的筹款产品，订单数据保存的数据库。0表示正常，未迁移 |
| 92 | flg | int | 1 |  | 0 | 新增第三方产品时用（3：联劝网项目；4：联劝网月捐项目；5：非联劝网项目） |

### 公益活动表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | activityId | varchar | 32 |  |  | 活动id（act开头) |
| 3 | activityName | varchar | 20 | 否 |  | 活动名称 |
| 4 | subjectPicId | int | 11 |  |  | 主题图片ID，列表页面（主题图片比例为8：9） |
| 5 | subjectBigPicId | int | 11 |  |  | 主题图片ID，详情页面（主题图片比例为4:3） |
| 6 | activityIntroduction | text |  |  |  | 活动简介 |
| 7 | startTime | datetime |  |  |  | 活动开始时间 |
| 8 | endTime | datetime |  |  |  | 活动结束时间 |
| 9 | keywords1 | int | 11 |  |  | 关键字1 |
| 10 | keywords2 | int | 11 |  |  | 关键字2 |
| 11 | keywords3 | int | 11 |  |  | 关键字3 |
| 12 | keywords4 | int | 11 |  |  | 关键字4 |
| 13 | keywords5 | int | 11 |  |  | 关键字5 |
| 14 | status | int | 1 | 否 | 0 | 活动状态（0：未申请；1：在线申请中；2：在线上（审批通过）；3：中途取消；4：线下活动；5：审核不通过；6：已结束；7：测试发布；8：已删除；9：第三方平台（联劝网）） |
| 15 | institutionId | int | 11 | 否 |  | 机构ID |
| 16 | reviewDetail | text |  |  |  | 审核不通过的理由 |
| 17 | reviewTime | datetime |  |  |  | 审核时间 |
| 18 | reviewer | varchar | 32 |  |  | 审核者（用户名） |
| 19 | reviewNickName | varchar | 32 |  |  | 审核者昵称 |
| 20 | applyTime | datetime |  |  |  | 申请时间 |
| 21 | fundraisingAccount | varchar | 32 |  |  | 筹款账户 |
| 22 | fundraisingStartTime | datetime |  |  |  | 筹款开始时间 |
| 23 | fundraisingEndTime | datetime |  |  |  | 筹款截止时间 |
| 24 | isHaveWebSite | int | 1 | 否 | 1 | 是否有独立网站（0：有独立网站；1：没有独立网站）_x000D_ 有独立网站时，经由独立网站完成报名捐赠；_x000D_ 没有独立网站时，经由联劝的报名网站完成报名捐赠； |
| 25 | webSiteUrl | varchar | 500 |  |  | 独立网站访问地址（PC版） |
| 26 | webSiteUrlM | varchar | 200 |  |  | 独立网站访问地址（PAD版、手机版） |
| 27 | showTime | datetime |  |  |  | 活动线上展示时间 |
| 28 | useAds | int | 1 | 否 | 0 | 报名类型（0：由报名捐赠系统实现报名捐赠；1：不需要报名捐赠系统实现；2：由报名捐赠系统实现捐赠0：组队报名；1：不需要报名；2：个人报名；99：义卖活动） |
| 29 | createTime | datetime |  |  |  | 活动创建时间 |
| 30 | anonymousAccount | varchar | 32 |  |  | 匿名捐赠账户 |
| 31 | drawAccount | varchar | 32 |  |  | 抽签费账户 |
| 32 | registrationAccount | varchar | 32 |  |  | 报名费账号（要在线报名并需要支付报名费时为必须项，审批通过时开通） |
| 33 | version | varchar | 10 | 否 |  | 爱扑满版本号 |
| 34 | businessid | varchar | 32 | 否 |  | 感谢卡模板id |
| 35 | closeReanson | varchar | 200 |  |  | 取消理由 |
| 36 | closeTime | datetime |  |  |  | 取消时间 |
| 37 | closer | varchar | 32 |  |  | 取消者（机构名称或管理员用户名） |
| 38 | target1 | decimal | 15,2 |  | 20 | 筹款标的1 |
| 39 | target2 | decimal | 15,2 |  | 50 | 筹款标的2 |
| 40 | target3 | decimal | 15,2 |  | 100 | 筹款标的3 |
| 41 | indexShowOrder | int | 11 |  |  | 首页显示顺 |
| 42 | holderId | int | 11 |  |  | 所属ID（填写所属基金ID、所属专项合作ID） |
| 43 | type | int | 1 | 否 | 1 | 所属类型（1：独立活动；2：基金；3：合作；4：潜在资助机构；5：公募基金…） |
| 44 | pointTo | int | 1 | 否 |  | 用款指向（1：专款专用；2：无指向性） |
| 45 | snakerOrderId | varchar | 32 |  |  | 流程实例ID |
| 46 | snakerStepName | varchar | 100 |  |  | 当前任务名 |
| 47 | snakerStatus | varchar | 100 |  |  | 当前状态 |
| 48 | adminFeeType | tinyint | 1 |  |  | 行政管理费提取类型（0：不提取1：固定比例提取2：累进金额按约定比例提取） |
| 49 | adminFeeFixedRate | decimal | 5,2 |  |  | 行政管理费固定提取比例(非固定比例为空) |
| 50 | checkCode | varchar | 32 |  |  | 校验码 |
| 51 | codeInvalidTime | datetime |  |  |  | 校验码无效时间 |
| 52 | cycleEndTime | datetime |  |  |  | 活动最终结束时间 |
| 53 | mailjobreviewId | int | 11 |  |  | 活动反馈邮件任务ID（0：未生成；-1：已删除；-2：生成失败；-3：邮件组不存在；>0：已生成） |
| 54 | contacts | varchar | 100 |  |  | 主办方联系方式/客服电话 |
| 55 | ifNeedPhoneAM | int | 1 | 否 | 0 | 匿名捐赠是否必填手机（0：非必填；1：必填） |
| 56 | ifNeedEmailAM | int | 1 | 否 | 0 | 匿名捐赠是否必填邮箱（0：非必填；1：必填） |
| 57 | ifNeedFund | int | 1 | 否 | 0 | 是否需要筹款（0：要筹款；1：不要筹款） |
| 58 | videoId | varchar | 32 |  |  | 视频vid |
| 59 | showVideoFlag | int | 1 |  |  | 是否显示视频（0：不显示；1：显示） |
| 60 | recordNumber | varchar | 255 |  |  | 备案编号 |
| 61 | supportEn | int | 1 | 否 | 0 | 是否支持英文版（0：不支持；1：支持） |
| 62 | adminFeeFixedRateRq | decimal | 5,2 |  |  | 报名费行政管理费固定提取比例(非固定比例为空or0) |
| 63 | logisticalAccount | varchar | 32 |  |  | 快递费账户（义买活动时存在） |
| 64 | publishPlatformId | varchar | 64 |  |  | 项目发布平台ID(为空时，联劝网线上发布) |
| 65 | webSite | int | 1 |  |  | 筹款网址 |
| 66 | anonymousLotteryFeeAccount | varchar | 32 |  |  | 匿名抽签费账户 |
| 67 | anonymousRegistrationFeeAccount | varchar | 32 |  |  | 匿名报名费账户 |
| 68 | firstActivityName | varchar | 255 |  |  | 第一次录入的项目名称 |
| 69 | mark | int | 11 |  |  | 筹款是否已结束的标志位（0：未筹款；1：筹款中；2：筹款结束；） |
| 70 | flg | int | 1 |  | 1 | 新增第三方产品时用（1：联劝网活动；2：联劝网义买；） |
| 71 | charger | varchar | 32 |  |  | 活动负责人 |
| 72 | fundraisingMaxAmount | decimal | 15,2 |  |  | 筹款目标（根据项目筹款结束方式设置，可以为空） |

### 公益基金表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | fundId | varchar | 32 | 否 |  | 基金ID（FUND开头的） |
| 3 | fundName | varchar | 32 | 否 |  | 基金名称 |
| 4 | fundAbbreviation | varchar | 12 |  |  | 基金简称 |
| 5 | fundLogo | int | 11 |  |  | 主题LOGO，列表页面（主题图片比例为8：9） |
| 6 | fundBigPicId | int | 11 |  |  | 主题图片ID，详情页面（主题图片比例为4:3） |
| 7 | fundIntroduction | text |  |  |  | 基金简介 |
| 8 | fundDetail | text |  |  |  | 基金详情 |
| 9 | fundUse | text |  |  |  | 资金使用 |
| 10 | fundTarget1 | decimal | 15,2 |  | 10 | 筹款标的1 |
| 11 | fundTarget2 | decimal | 15,2 |  | 20 | 筹款标的2 |
| 12 | fundTarget3 | decimal | 15,2 |  | 50 | 筹款标的3 |
| 13 | keywords1 | varchar | 5 |  |  | 关键字1 |
| 14 | keywords2 | varchar | 5 |  |  | 关键字2 |
| 15 | keywords3 | varchar | 5 |  |  | 关键字3 |
| 16 | fundraisingMaxAmount | decimal | 15,2 |  |  | 筹款限额 |
| 17 | fundStyle | int | 1 |  |  | 基金领域 |
| 18 | status | int | 1 | 否 | 0 | 基金状态（0：未发布；1：已发布；2：已撤消；3：审核不通过） |
| 19 | institutionId | int | 11 |  |  | 机构ID |
| 20 | institutionIntroduction | text |  |  |  | 机构简介 |
| 21 | institutionShowFlag | int | 1 |  | 0 | 是否显示机构（0：不显示；1：显示） |
| 22 | accountAll | varchar | 32 |  |  | 基金账户（总账户） |
| 23 | accountOnLine | varchar | 32 |  |  | 联劝网平台账户(线上账户) |
| 24 | accountOffLine | varchar | 32 |  |  | 第三方平台账户（线下账户） |
| 25 | createTime | datetime |  |  |  | 创建时间 |
| 26 | applyTime | datetime |  |  |  | 发布时间 |
| 27 | applyer | varchar | 32 |  |  | 发布者（用户名） |
| 28 | applyNickName | varchar | 32 |  |  | 发布者昵称 |
| 29 | cancelReason | varchar | 200 |  |  | 撤消理由 |
| 30 | cancelTime | datetime |  |  |  | 撤消时间 |
| 31 | anonymousAccount | varchar | 32 |  |  | 匿名捐赠账户 |
| 32 | adminFeeType | tinyint | 1 |  |  | 行政管理费提取类型（0：不提取 1：固定比例提取 2：累进金额按约定比例提取 ） |
| 33 | adminFeeFixedRate | decimal | 5,2 |  |  | 行政管理费固定提取比例(非固定比例为空) |
| 34 | keywordsId1 | int | 11 |  |  | 关键字Id1 |
| 35 | keywordsId2 | int | 11 |  |  | 关键字Id2 |
| 36 | keywordsId3 | int | 11 |  |  | 关键字Id3 |
| 37 | showFlag | int | 1 | 否 | 0 | 联劝网是否显示（0：不显示；1：显示） |
| 38 | indexShowOrder | int | 11 |  |  | 首页显示顺 |
| 39 | type | int | 11 |  | 1 | 基金类别（1：公益基金；2：企业基金；3：联劝自发（联劝统筹）；4：联劝网基金（没有数据）；5：捐赠人建议基金）（标签系统用 99：公募基金，公募基金信息记录在公募基金表） |
| 40 | ifNeedPhoneAM | int | 1 | 否 | 0 | 匿名捐赠是否必填手机（0：非必填；1：必填） |
| 41 | ifNeedEmailAM | int | 1 | 否 | 0 | 匿名捐赠是否必填邮箱（0：非必填；1：必填） |
| 42 | needContract | int | 1 | 否 | 0 | 是否开通月捐（0：不开通；1：开通；2：审核中；3：审核不通过；4：审核通过；5：停止） |
| 43 | accountContract | varchar | 32 |  |  | 月捐账户 |
| 44 | wxPlanId | varchar | 28 |  |  | 微信月捐模板ID |
| 45 | ifNeedFund | int | 1 | 否 | 0 | 是否需要筹款（0：要筹款；1：不要筹款） |
| 46 | fundLogoedit | int | 11 |  |  | 审核前备份图片ID |
| 47 | fundBigPicIdedit | int | 11 |  |  | 审核前备份图片ID |
| 48 | recordNumber | varchar | 255 |  |  | 备案编号 |
| 49 | isSingleDonate | int | 2 | 否 | 1 | 是否需要单笔捐（1：需要；2：不需要） |
| 50 | planType | int | 1 |  |  | 联劝统筹类型（0：统筹资助；1：统筹合作） |
| 51 | buildTime | date | 0 |  |  | 基金设立日期 |
| 52 | limitTime | date | 0 |  |  | 认证截止有效日期（为空表示长期） |

### 项目实行表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | projectId | int | 11 | 否 |  | 项目ID |
| 3 | applyTimeStart | datetime |  |  |  | 募集执行开始日 |
| 4 | applyTimeEnd | datetime |  |  |  | 募集执行结束日 |
| 5 | budgetAmountTotal | decimal | 15,2 |  | 0 | 筹款目标（实施计划的预算合计） |
| 6 | expirationTime | datetime |  |  |  | 筹款截止时间 |
| 7 | projectStage | tinyint | 1 | 否 | 0 | 项目阶段（0：未申报；1：申报中；2：募集中；3：执行中；4：已结题；5：中止申请中；6：申请后的中止；7：系统主动中止；） |
| 8 | projectPicId | int | 11 |  | 0 | 项目主题图片ID（4;3）（项目申报时不能为空） |
| 9 | periods | int | 2 | 否 | 1 | 项目资金拨付当前期数 |
| 10 | minDonateAmount | decimal | 15,2 |  | 0 | 允许用户评论的最低捐款额 |
| 11 | entityGuid | varchar | 32 |  |  | 项目实行期实体ID（建资金账户用） |
| 12 | fundraisingAccount | varchar | 32 |  |  | 筹款账户（临时） |
| 13 | projectPicENId | int | 11 |  | 0 | 项目主题图片ID（8：9）（项目申报时不能为空） |
| 14 | planningAmount | decimal | 15,2 |  | 0 | 实施计划预算总额 |
| 15 | otherCostsAmount | decimal | 15,2 |  | 0 | 其他费用支出总额 |
| 16 | fundBudgetAmount | decimal | 15,2 |  | 0 | 筹款预算总额 |
| 17 | managerAmount | decimal | 15,2 |  | 0 | 管理费（金额） |
| 18 | clearSourceAmount | decimal | 15,2 |  |  | 管理费有来源的费用的金额 |
| 19 | clearSourcememo | text | 150 |  |  | 管理费有来源的费用的说明 |
| 20 | taxationPer | decimal | 5,4 |  | 0 | 税费（百分比，单位%） |
| 21 | taxationAmount | decimal | 15,2 |  | 0 | 税费（金额） |
| 22 | versionAgencies | int | 11 |  |  | 审核的项目版本ID |
| 23 | version | int | 11 | 否 | 1 | 最新的项目版本ID |
| 24 | applyCreateTime | datetime |  |  |  | 项目执行项目创建时间 |
| 25 | applyUpdateTime | datetime |  |  |  | 项目执行项目更新时间 |
| 26 | effectiveInvalidFlag | tinyint | 1 |  | 1 | 有效/无效标志（0：无效；1：有效） |
| 27 | remarks | varchar | 255 |  |  | 备注 |
| 28 | reportType | int | 2 | 否 | 0 | 项目申报类型 |
| 29 | bank | varchar | 32 |  |  | 开户银行 |
| 30 | accountName | varchar | 32 |  |  | 账户名称（即收款单位） |
| 31 | bankAccount | varchar | 30 |  |  | 银行帐号 |
| 32 | bankNo | varchar | 32 |  |  | 银行行号 |
| 33 | serialNo | varchar | 32 | 否 |  | 资金拨付流水号（多期时，此为最新一期的流水号；申请时更新到拨付申请表） |
| 34 | newFlowFlag | int | 2 |  |  | 新的流程标志位（0：原来的申报流程；1：新的项目资助申报流程） |
| 35 | applicant | varchar | 32 |  |  | 申请人 |
| 36 | appropriationStatus | int | 2 |  |  | 拨付状态 |
| 37 | feedbackStartTime | int | 2 |  |  | 下一次反馈提醒时间 |
| 38 | lastFeedbackTime | int | 2 |  |  | 最近一次反馈通过时间 |
| 39 | needRemindStatus | int | 2 |  |  | 需要反馈状态 0:无需月度反馈提醒，1；开始月度反馈提醒；2：月度反馈提醒结束; |
| 40 | feedbackNumber | int | 2 |  |  | 线下捐赠已检测提醒次数 |
| 41 | stopStatus | tinyint | 4 |  |  | 是否终止（99：终止） |
| 42 | stopReason | varchar | 500 |  |  | 终止理由 |
| 43 | stopOperator | varchar | 64 |  |  | 操作者 |
| 44 | financialFirstPassTime | datetime | 0 |  |  | 财务初审通过时间 |
| 45 | financialReviewPassTime | datetime | 0 |  |  | 财务复审通过时间 |
| 46 | project_clerk_user | varchar | 32 |  |  | 最新的项目初审者 |
| 47 | project_manage_user | varchar | 32 |  |  | 最新的项目复审者 |

### 专项合作表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | cooperationId | varchar | 32 | 否 |  | 合作ID（COOP开头的） |
| 3 | cooperationName | varchar | 32 | 否 |  | 合作名称 |
| 4 | cooperationAbbreviation | varchar | 12 |  |  | 合作简称 |
| 5 | cooperationLogo | int | 11 |  |  | 主题LOGO，列表页面（主题图片比例为8：9） |
| 6 | cooperationBigPicId | int | 11 |  |  | 主题图片ID，详情页面（主题图片比例为4:3） |
| 7 | cooperationIntroduction | text |  |  |  | 合作简介 |
| 8 | cooperationDetail | text |  |  |  | 合作详情 |
| 9 | fundUse | text |  |  |  | 资金使用 |
| 10 | target1 | decimal | 15,2 |  | 10 | 筹款标的1 |
| 11 | target2 | decimal | 15,2 |  | 20 | 筹款标的2 |
| 12 | target3 | decimal | 15,2 |  | 50 | 筹款标的3 |
| 13 | keywords1 | int | 11 |  |  | 关键字1 |
| 14 | keywords2 | int | 11 |  |  | 关键字2 |
| 15 | keywords3 | int | 11 |  |  | 关键字3 |
| 16 | fundraisingMaxAmount | decimal | 15,2 |  |  | 筹款限额 |
| 17 | cooperationStyle | int | 1 |  |  | 合作领域 |
| 18 | status | int | 1 | 否 | 0 | 合作状态（0：未发布；1：已发布；2：已撤消；） |
| 19 | institutionId | int | 11 |  |  | 机构ID |
| 20 | institutionShowFlag | int | 1 |  | 0 | 是否显示机构（0：不显示；1：显示） |
| 21 | accountAll | varchar | 32 |  |  | 合作账户（总账户） |
| 22 | accountOnLine | varchar | 32 |  |  | 联劝网平台账户(线上账户) |
| 23 | accountOffLine | varchar | 32 |  |  | 第三方平台账户（线下账户） |
| 24 | createTime | datetime |  |  |  | 创建时间 |
| 25 | applyTime | datetime |  |  |  | 发布时间 |
| 26 | applyer | varchar | 32 |  |  | 发布者（用户名） |
| 27 | applyNickName | varchar | 32 |  |  | 发布者昵称 |
| 28 | cancelReason | varchar | 200 |  |  | 撤消理由 |
| 29 | cancelTime | datetime |  |  |  | 撤消时间 |
| 30 | anonymousAccount | varchar | 32 |  |  | 匿名捐赠账户 |
| 31 | adminFeeType | tinyint | 1 |  |  | 行政管理费提取类型（0：不提取 1：固定比例提取 2：累进金额按约定比例提取 ） |
| 32 | adminFeeFixedRate | decimal | 5,2 |  |  | 行政管理费固定提取比例(非固定比例为空) |
| 33 | showFlag | int | 1 | 否 | 0 | 联劝网是否显示（0：不显示；1：显示） |
| 34 | type | int | 11 |  | 1 | 合作类别（1：公益合作；2：企业合作；3：联劝自发（没有数据）；4：联劝网合作；） |
| 35 | ifNeedPhoneAM | int | 1 | 否 | 0 | 匿名捐赠是否必填手机（0：非必填；1：必填） |
| 36 | ifNeedEmailAM | int | 1 | 否 | 0 | 匿名捐赠是否必填邮箱（0：非必填；1：必填） |
| 37 | ifNeedFund | int | 1 | 否 | 0 | 是否需要筹款（0：要筹款；1：不要筹款） |
| 38 | cooperationLogoedit | int | 11 |  |  | 审核前备份图片ID |
| 39 | cooperationBigPicIdedit | int | 11 |  |  | 审核前备份图片ID |
| 40 | needContract | int | 1 | 否 | 0 | 是否开通月捐（0：不开通；1：开通；2：审核中；3：审核不通过；4：审核通过；5：停止） |
| 41 | accountContract | varchar | 32 | 是 |  | 月捐账户 |
| 42 | wxPlanId | varchar | 28 | 是 |  | 微信月捐模板ID |
| 43 | recordNumber | varchar | 255 |  |  | 备案编号 |
| 44 | isSingleDonate | int | 2 | 否 | 1 | 是否需要单笔捐（1：需要；2：不需要） |
| 45 | buildTime | date | 0 |  |  | 基金设立日期 |
| 46 | limitTime | date | 0 |  |  | 认证截止有效日期（为空表示长期） |

### 函数目录

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | t_banyan_contract_config
t_banyan_cooperation
t_banyan_donate_config_definition
t_banyan_fulcommonweal_project_progress
t_banyan_fund
t_banyan_institution
user_institution_contrast
horn.donation_field
horn.field_project
horn.t_horn_contract
horn.t_horn_donate_config_definition
horn.t_horn_fulcommonweal_project
horn.t_horn_series_activity 
cello.t_cello_account 
p_copeContractInfo_5_contractuser
p_copeProgressInfo |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 2 | t_banyan_contract_user_poster
horn.t_horn_contract_user_poster |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 3 | t_banyan_file
t_banyan_institution
t_banyan_registration_info
user_institution_contrast
scrm.crmuser
scrm.file
scrm.institution
scrm.institution_platform
scrm.institutionInfo |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 4 | t_banyan_fulcommonweal_project_progress
t_banyan_fulcommonweal_project_progress_detail
horn.t_horn_fulcommonweal_progress
horn.t_horn_fulcommonweal_progress_detail |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 5 | t_banyan_cooperation
t_banyan_donate_config_definition
t_banyan_fulcommonweal_project
t_banyan_fulcommonweal_project_budget
t_banyan_fulcommonweal_project_progress
t_banyan_fund
t_banyan_institution
user_institution_contrast
horn.donation_field
horn.field_project
horn.t_horn_donate_config_definition
horn.t_horn_fulcommonweal_project
horn.t_horn_fulcommonweal_project_budget
p_copeProgressInfo |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 6 | t_banyan_institution
t_banyan_user
user_institution_contrast
horn.user_role
scrm.crmuser
scrm.institution |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 7 | t_banyan_group_event_status
p_get_special_fund_withdrawals |  |  |  |  | 保证最后一次的执行都是正常更新，有错误，则停止 |
| 8 | t_commodity_event_status
p_commodity_order_seccess |  |  |  |  | 删除,义卖已迁移到horn，现在已无调用 |
| 9 | wf_hist_task
wf_task |  |  |  |  |  |
| 10 | t_banyan_allocated_apply
t_banyan_financial_report
t_banyan_fund_allocation
t_banyan_fund_allocation_actual
t_banyan_group_event_message
t_banyan_periodical_report
t_banyan_project_execution
t_banyan_withdrawal
taxus.account 
taxus.entity
p_need_feedback_or_not
p_get_feedback_through_time |  |  |  |  |  |
| 11 | orderinfo_message |  |  |  |  | 删除,义卖已迁移到horn，现在已无调用,错误信息收集表已被删除 |
| 12 | t_banyan_commodity
t_banyan_commodity_orderinfo
t_banyan_orderinfo |  |  |  |  | 删除,义卖已迁移到horn，现在已无调用 |
| 13 | t_banyan_request_news_day_statistical
t_banyan_request_news_month_statistical
t_banyan_request_page_day_statistical
t_banyan_request_page_month_statistical |  |  |  |  | 删除，已无统计意义 |
| 14 | t_banyan_allocated_apply
t_banyan_dic_region
t_banyan_fund_allocation_actual
t_banyan_fund_allocation_location |  |  |  |  | 删除 |
| 15 | t_banyan_dic_region
t_banyan_project
t_banyan_project_apply
t_banyan_project_appropriation
t_banyan_project_info
t_banyan_project_location |  |  |  |  | 删除 |
| 16 | wf_hist_task |  |  |  |  |  |
| 17 | t_banyan_request_info
t_banyan_request_news_day_statistical
t_banyan_request_page_day_statistical
p_i_page_news_month_count |  |  |  |  | 删除，已无统计意义 |
| 18 | t_banyan_request_info
t_banyan_request_news_day_statistical
t_banyan_request_page_day_statistical
p_i_page_news_month_count |  |  |  |  | 删除，已无统计意义 |
| 19 | p_i_page_news_month_count |  |  |  |  | 删除 |
| 20 | t_banyan_project_count
p_i_project_count
p_i_project_count2 |  |  |  |  | 删除 |
| 21 | t_banyan_institution
t_banyan_registration_info
scrm.black_list
scrm.channel
scrm.charge_info
scrm.contact_plan
scrm.contacts
scrm.crmuser
scrm.department
scrm.donor
scrm.donor_repeat
scrm.group
scrm.group_synchro
scrm.ins_account
scrm.institution
user_institution_contrast
scrm.institutionInfo
scrm.invoice_msg
scrm.label
scrm.label_contacts
scrm.mail_fee
scrm.mail_history
scrm.mail_queue
scrm.mail_stat
scrm.mail_stat_count 
scrm.other_stat
scrm.questionnaire_result_stat 
scrm.role 
scrm.sms_fee
scrm.sms_queue
scrm.sms_stat
scrm.sms_stat_count 
scrm.sms_template
scrm.spread 
scrm.tel_stat 
scrm.temporaryDonor
scrm.user_info 
scrm.wx_stat 
scrm.ins_wallet 
scrm.institution_platform 
scrm.invoice 
scrm.rechargeinfo |  |  |  |  | 删除,迁移到scrm时用过，现在已无调用 |
| 22 | t_banyan_administration_budget |  |  |  |  |  |
| 23 | t_banyan_cooperation
t_banyan_field
t_banyan_fund_allocation
t_banyan_institution
temp_fund_coo_field
taxus.account
taxus.entity |  |  |  |  | 删除 |
| 24 | ads.t_egg_user
cas.user_info
taxus.entity
taxus.orderinfo |  |  |  |  | 删除 |
| 25 | t_banyan_cooperation
t_banyan_fulcommonweal_project
t_banyan_fund
t_banyan_fund_allocation
t_banyan_institution
temp_fund_coo_field
taxus.account
taxus.entity |  |  |  |  | 删除 |
| 26 | t_banyan_activity
t_xugj_file
t_xugj_no_file |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 27 | t_banyan_activity_detail
t_xugj_file |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 28 | t_banyan_annual_reports
t_xugj_file |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 29 | t_banyan_project_apply |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 30 | t_banyan_cooperation
t_xugj_file
t_xugj_no_file |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 31 | t_banyan_file_delete |  |  |  |  | 删除,迁移到horn时用过，现在已无调用，已无该表 |
| 32 | t_banyan_file_old |  |  |  |  | 删除,迁移到horn时用过，现在已无调用，已无该表 |
| 33 | t_banyan_execution_pic
t_xugj_file
t_xugj_no_file |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 34 | t_banyan_fulcommonweal_project
t_xugj_file
t_xugj_no_file |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 35 | t_banyan_fulcommonweal_project
t_banyan_fulcommonweal_project_detail
t_xugj_file
t_xugj_no_file |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 36 | t_banyan_fulcommonweal_project_progress
t_xugj_file
t_xugj_no_file |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 37 | t_banyan_fulcommonweal_project_progress_detail
t_xugj_file
t_xugj_no_file |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 38 | t_banyan_fund
t_xugj_file
t_xugj_no_file |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 39 | t_banyan_fund_cooperation_detail
t_xugj_file
t_xugj_no_file |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 40 | t_banyan_institution_member
t_xugj_file
t_xugj_no_file |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 41 | t_banyan_introduction
t_xugj_file
t_xugj_no_file |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 42 | t_banyan_jointDonation
t_xugj_file
t_xugj_no_file |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 43 | t_banyan_manager
t_xugj_file
t_xugj_no_file |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 44 | t_banyan_file
t_banyan_file_delete
t_banyan_file_temp
t_xugj_file
move_id |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |
| 45 | t_banyan_registration_info
t_xugj_file
t_xugj_no_file |  |  |  |  | 删除,迁移到horn时用过，现在已无调用 |

### 活动报销申请表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | activityId | varchar | 32 |  |  | 要报销的费用对应的活动id |
| 3 | activityName | varchar | 64 |  |  | 活动名称 |
| 4 | activityLeader | varchar | 32 |  |  | 活动负责人 |
| 5 | activityLeaderGuid | varchar | 32 |  |  | 活动负责人guid |
| 6 | deptId | int | 11 |  |  | 部门id（报销类别为项目时，要选择部门；报销类别为活动时，默认为原资拓部（id：3）） 为确定流程中的部门负责人 |
| 7 | payeeNameGuid | varchar | 32 |  |  | 收款人guid |
| 8 | snakerOrderId | varchar | 32 |  |  | 流程实例ID |
| 9 | operatorUserId | varchar | 32 |  |  | 操作者用户名 |
| 10 | operatorNickName | varchar | 32 |  |  | 操作者昵称 |
| 11 | serialNo | varchar | 32 |  |  | 资金拨付流水号 |
| 12 | deleteFlag | int | 1 | 否 | 0 | 删除标记位（0：正常 1：删除） |
| 13 | securityCode | varchar | 512 |  |  | 防伪码 |
| 14 | createTime | datetime | 0 |  |  | 创建时间 |
| 15 | applyTime | datetime | 0 |  |  | 申请时间 |
| 16 | applyAmount | decimal | 15，2 |  |  | 申请金额 |
| 17 | projectId | varchar | 255 |  |  | 虚拟申报项目guid |
| 18 | actualAccount | decimal | 15，2 |  |  | 实际拨付金额 |
| 19 | finshFlag | int | 1 |  | 0 | 0:没流程；1：流程进行中；3：流程终止；4：出纳拨付失败；5：收款人收款失败；6：剩余资金结转失败 |
| 20 | remarks | varchar | 255 |  |  | 备注 |
| 21 | expenseAmount | decimal | 15，2 |  |  | 出纳实际拨付金额 |
| 22 | expenseTime | datetime | 0 |  |  | 出纳实际拨付时间 |
| 23 | receivableAccount | varchar | 32 |  |  | 申报项目收款账户 |
| 24 | bank | varchar | 32 |  |  | 收款开户银行 |
| 25 | payeeName | varchar | 30 |  |  | 收款人姓名 |
| 26 | bankAccount | varchar | 32 |  |  | 收款银行账号 |
| 27 | bankNo | varchar | 32 |  |  | 收款银行行号 |
| 28 | actOrPro | int | 11 |  |  | 报销类别（0：活动；1：项目；） |
| 29 | fields | varchar | 10 |  |  | 领域 |
| 30 | refundFlg | int | 11 | 否 | 0 | 是否执行退款（0：未执行退款；-1：已执行退款；其他情况下存放退款项目id；） |
| 31 | financialFirstPassTime | datetime | 0 |  |  | 财务初审通过时间 |
| 32 | financialReviewPassTime | datetime | 0 |  |  | 财务复审通过时间 |

### 资金拨付表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | projectId | varchar | 32 |  |  | 项目ID（PJT开头） |
| 3 | projectName | varchar | 64 | 否 |  | 项目名称 |
| 4 | projectSummary | text |  | 否 |  | 项目概述 |
| 5 | applyGrantTotal | decimal | 15,2 | 否 |  | 申请拨款总额 |
| 6 | applyGrantPeriods | int | 2 | 否 |  | 申请拨款期数（1：一次性拨付 2：分多次拨付） |
| 7 | grantTotalAccount | varchar | 32 |  |  | 项目接受拨款总额账户 |
| 8 | periodsAccount | varchar | 32 |  |  | 项目执行账户 |
| 9 | institutionId | int | 11 | 否 |  | 机构ID |
| 10 | targetsTypeId | int | 2 | 否 |  | 申报对象类型ID（基金、合作、活动，设置值参见taxus的实体类型表中的值） |
| 11 | targetsType | varchar | 32 | 否 |  | 申报对象类型名称 |
| 12 | targetsId | varchar | 32 | 否 |  | 申报对象ID（根据类型而定，如基金ID、合作ID、活动ID、项目ID） |
| 13 | targets | varchar | 32 | 否 |  | 申报对象名称 |
| 14 | targetsHoldTypeId | int | 2 | 否 |  | 申报对象所属类型ID（基金、合作的实体类型） |
| 15 | targetsHoldId | varchar | 32 | 否 |  | 申报对象所属ID（根据类型而定，如基金ID、合作ID） |
| 16 | projectCreateTime | datetime |  |  |  | 项目创建时间 |
| 17 | snakerOrderId | varchar | 32 |  |  | 流程实例ID |
| 18 | operatorUserId | varchar | 30 | 否 |  | 操作者用户名 |
| 19 | operatorNickName | varchar | 30 | 否 |  | 操作者昵称 |
| 20 | materialsFileId | int | 11 |  |  | 终止材料（附件ID，附件内容保存在文件表中） |
| 21 | status | int | 1 |  |  | 状态（0：流程未开始；1：流程中；2：流程终止；3:流程中，已开户） |
| 22 | fields | varchar | 32 |  |  | 项目领域 |
| 23 | serialNo | varchar | 32 |  |  | 资金拨付流水号（多期时，此为最新一期的流水号；申请时更新到拨付申请表） |
| 24 | deleteFlag | int | 1 | 否 |  | 删除标记位（0：正常 1：删除） |
| 25 | securityCode | varchar | 512 |  |  | 防伪码 |
| 26 | targetsProperty | varchar | 10 | 否 |  | 申报对象属性（10：筹款 18：报名费/抽签费） |
| 27 | mediumTermFlag | int | 1 |  |  | 区分是否需要中期资金有报告（0：有；1：没有） |
| 28 | stopReason | varchar | 500 |  |  | 终止理由 |
| 29 | stopOperator | varchar | 64 |  |  | 终止操作者 |
| 30 | financialFirstPassTime | datetime | 0 |  |  | 财务初审通过时间 |
| 31 | financialReviewPassTime | datetime | 0 |  |  | 财务复审通过时间 |

### 新闻表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | title | varchar | 100 | 否 |  | 新闻主标题 |
| 3 | sourceid | int | 1 | 否 |  | 是否原创（0：原则；1：转载；2：单附件） |
| 4 | newssource | varcher | 32 |  |  | 新闻来源 |
| 5 | newsurl | varcher | 255 |  | 0 | 新闻来源链接 |
| 6 | summary | varcher | 255 |  |  | 新闻提要 |
| 7 | releaseTime | datetime |  | 否 |  | 新闻发布时间 |
| 8 | type | int | 1 |  |  | 所属类型（1：新闻； 2：他方链接…） |
| 9 | status | int | 1 |  |  | 新闻状态（0：草稿；1：初审中；2：初审未通过；3：最终审核中、初核通过；4：终审通过；5：终审未通过；6：删除；7：作废（审核通过后，再次更新内容时，当前条作废，重新生成一条）） |
| 10 | insertTime | datetime |  |  |  | 创建时间 |
| 11 | inserter | varchar | 32 |  |  | 创建者（用户名） |
| 12 | insertNickName | varchar | 32 |  |  | 创建者昵称 |
| 13 | applyTime | datetime |  |  |  | 申请时间 |
| 14 | reviewDetail | varchar | 200 |  |  | 初审不通过的理由 |
| 15 | reviewTime | datetime |  |  |  | 初审时间 |
| 16 | reviewer | varchar | 32 |  |  | 初审者（用户名） |
| 17 | reviewNickName | varchar | 32 |  |  | 初审者昵称 |
| 18 | reviewDetail2 | varchar | 200 |  |  | 终审不通过的理由 |
| 19 | reviewTime2 | datetime |  |  |  | 终审时间 |
| 20 | reviewer2 | varchar | 32 |  |  | 终审者（用户名） |
| 21 | reviewNickName2 | varchar | 32 |  |  | 终审者昵称 |
| 22 | mark | int | 1 |  |  | 标志（1：红；2：黄；3：绿；0：无） |
| 23 | keywords1 | varchar | 10 |  |  | 关键字1 |
| 24 | keywords2 | varchar | 10 |  |  | 关键字2 |
| 25 | keywords3 | varchar | 10 |  |  | 关键字3 |
| 26 | counts | int | 11 |  |  | 访问量 |
| 27 | iftemplate | int | 1 |  |  | 是否使用模板，用于判断新老数据的详情处理（0：不使用；1：使用） |
| 28 | pic | longblob |  |  |  | 图片内容（字节流） |
| 29 | picExt | varchar | 10 |  |  | 图片类型 |
| 30 | picName | varchar | 255 |  |  | 图片名称 |
| 31 | eggbzShowPic | int | 11 |  |  | 鸡蛋首页是否显示图片 （0表示显示图片，1表示不显示图片） |

### 公益账房票据申请信息表

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int |  | 否 |  | id |
| 2 | invoice_id | varchar | 64 | 否 |  | 申请id |
| 3 | invoice_type | varchar | 32 |  |  | 发票类型（纸质:paper/电子electronic） |
| 4 | invoice_amount | decimal | 15,0 |  |  | 总金额（单位：分） |
| 5 | bill_cycle | varchar | 255 |  |  | 账期 |
| 6 | org_promote_id | varchar | 255 |  |  | 机构唯一标识 |
| 7 | shop_id | bigint |  |  |  | 店铺id |
| 8 | invoice_title | varchar | 255 |  |  | 发票抬头 |
| 9 | seller_nick | varchar | 255 |  |  | 商家昵称（旺旺ID，和账单中的商铺旺旺号保持一样） |
| 10 | shop_name | varchar | 255 |  |  | 店铺名称 |
| 11 | unified_credit_code | varchar | 255 |  |  | 社会统一信用代码 |
| 12 | account_bank | varchar | 255 |  |  | 开户行 |
| 13 | account_no | varchar | 255 |  |  | 开户行账号 |
| 14 | contact_name | varchar | 255 |  |  | 商家联系人 |
| 15 | contact_mobile | varchar | 255 |  |  | 商家联系人电话 |
| 16 | contact_address | varchar | 1024 |  |  | 商家联系人地址 |
| 17 | project_list | text |  |  |  | JSONArray格式（包含项目名称project_name、项目金额project_amount）金额的单位：分 |
| 18 | accept_operator | varchar | 255 |  |  | 受理操作人 |
| 19 | bill_file | text |  |  |  | 账单明细文件地址，行数据中携带制表符 |
| 20 | bill_detail_standard_file | text |  |  |  | 账单明细文件地址，行数据中不带制表符 |
| 21 | invoice_state | int | 1 |  |  | 发票状态，1:已受理 |
| 22 | apply_time | datetime |  |  |  | 申请时间（在阿里公益账房侧的申请时间） |
| 23 | invoice_do_state | tinyint |  |  |  | 开票结果（-1：待处理；0：已拒绝；1：已开票；2：处理中；3：申请异常 ） |
| 24 | reason | varchar | 255 |  |  | 开票失败说明 |
| 25 | sync_flag | tinyint |  | 否 |  | 同步标记，其他申请来源用（0：未同步；1：已同步；2：同步失败；） |
| 26 | sync_msg | text |  |  |  | 同步票据失败说明（保留最新那次） |
| 27 | sync_err_num | int |  |  |  | 同步票据失败次数（最多3次，到达后系统不再重复调用，需要手工处理） |
| 28 | dealed_num | int |  | 否 | 0 | 已经处理票据张数（开几个月份就是几张票据，开票回调后更新） |
| 29 | create_time | timestamp |  | 否 | CURRENT_TIMESTAMP | 创建时间 |
| 30 | update_time | timestamp |  | 否 | CURRENT_TIMESTAMP | 更新时间 |
| 31 | writeOffStatus | int |  |  | 0 | 核销状态（0：要核销；1：不核销） |


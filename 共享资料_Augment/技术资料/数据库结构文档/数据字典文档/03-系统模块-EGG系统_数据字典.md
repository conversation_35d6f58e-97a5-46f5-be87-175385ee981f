# EGG系统 数据字典

## 系统概述

**系统名称**: EGG系统  
**系统标识**: egg  
**文件来源**: egg_dataBaseModelDesign.xls  
**工作表总数**: 78  
**有效表数量**: 76  
**字段总数**: 845  

## 表结构清单

| 序号 | 表名 | 字段数 | 说明 |
|------|------|--------|------|
| 1 | 更新履历 | 73 |  |
| 2 | t_egg_activity | 39 |  |
| 3 | t_egg_user | 36 |  |
| 4 | t_egg_activity_group_team | 28 |  |
| 5 | t_egg_temp_team_list | 26 |  |
| 6 | notifications_queue | 24 |  |
| 7 | t_egg_achievem_config | 22 |  |
| 8 | t_egg_group | 18 |  |
| 9 | t_egg_invoice_temp_info | 17 |  |
| 10 | t_egg_activity_teamtype | 16 |  |
| 11 | notifications_wx | 16 |  |
| 12 | notifications_sms | 16 |  |
| 13 | t_egg_donation_enterprise | 15 |  |
| 14 | t_egg_urgent_notice | 15 |  |
| 15 | t_egg_blacklist | 15 |  |
| 16 | t_egg_certificate_file | 15 |  |
| 17 | t_egg_carousel_pictures | 14 |  |
| 18 | t_egg_activity_activityterm | 13 |  |
| 19 | t_egg_fund_project | 13 |  |
| 20 | t_egg_certificate_property | 13 |  |
| 21 | t_egg_draw_right | 12 |  |
| 22 | notifications_template | 12 |  |
| 23 | t_egg_user_team | 11 |  |
| 24 | t_egg_user_ranking | 11 |  |
| 25 | t_egg_historical_data | 11 |  |
| 26 | t_egg_reg_order | 11 |  |
| 27 | t_egg_certificate_template | 11 |  |
| 28 | t_egg_badge_user | 11 |  |
| 29 | 视图更新履历 | 10 |  |
| 30 | t_egg_punchset | 10 |  |
| 31 | t_egg_template_block | 10 |  |
| 32 | t_egg_team_user_pic_temp | 10 |  |
| 33 | t_egg_achievem | 9 |  |
| 34 | t_egg_team_ranking | 9 |  |
| 35 | t_egg_invitation | 9 |  |
| 36 | t_egg_pictures | 9 |  |
| 37 | t_egg_fund_use | 9 |  |
| 38 | t_egg_template_block_file | 9 |  |
| 39 | t_egg_squadron | 9 |  |
| 40 | t_egg_rank_user | 9 |  |
| 41 | t_egg_reg_order_sub | 9 |  |
| 42 | t_egg_activity_enterprise | 8 |  |
| 43 | t_egg_activity_user | 7 |  |
| 44 | t_egg_teamtype | 7 |  |
| 45 | t_egg_roadmap | 7 |  |
| 46 | t_egg_draw_introduction | 7 |  |
| 47 | t_egg_template | 7 |  |
| 48 | t_egg_timing_task | 7 |  |
| 49 | t_egg_team | 6 |  |
| 50 | t_egg_team_group | 6 |  |
| 51 | t_egg_achievem_original | 6 |  |
| 52 | t_egg_raisetarget_adjust | 6 |  |
| 53 | t_egg_squadron_team | 6 |  |
| 54 | t_egg_day_team_rank | 6 |  |
| 55 | t_egg_message | 6 |  |
| 56 | t_egg_project | 6 |  |
| 57 | t_egg_test_answer | 6 |  |
| 58 | t_egg_tests | 6 |  |
| 59 | t_egg_test_item | 6 |  |
| 60 | t_egg_request_info | 6 |  |
| 61 | t_egg_wx_personal_user | 6 |  |
| 62 | t_egg_donationtype | 5 |  |
| 63 | t_egg_webconfig | 5 |  |
| 64 | t_egg_fund_file | 5 |  |
| 65 | t_egg_team_FundAccount | 5 |  |
| 66 | t_egg_badge | 5 |  |
| 67 | t_egg_activity_mileage | 5 |  |
| 68 | t_egg_team_mileage | 5 |  |
| 69 | t_egg_reward_record | 5 |  |
| 70 | t_egg_activityterm | 4 |  |
| 71 | t_egg_activityterm_apply | 4 |  |
| 72 | t_egg_rank_team | 4 |  |
| 73 | t_egg_banwords | 4 |  |
| 74 | t_egg_group_pictures | 3 |  |
| 75 | t_egg_fund_region | 3 |  |
| 76 | t_egg_fund_field | 0 |  |

## 主要表结构详情

### 更新履历

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | - |  |  |  |  |  |
| 2 | - |  |  |  |  |  |
| 3 | - |  |  |  |  |  |
| 4 | - |  |  |  |  |  |
| 5 | - |  |  |  |  |  |
| 6 | - |  |  |  |  |  |
| 7 | - |  |  |  |  |  |
| 8 | - |  |  |  |  |  |
| 9 | - |  |  |  |  |  |
| 10 | - |  |  |  |  |  |
| 11 | - |  |  |  |  |  |
| 12 | - |  |  |  |  |  |
| 13 | - |  |  |  |  |  |
| 14 | - |  |  |  |  |  |
| 15 | - |  |  |  |  |  |
| 16 | - |  |  |  |  |  |
| 17 | - |  |  |  |  |  |
| 18 | - |  |  |  |  |  |
| 19 | - |  |  |  |  |  |
| 20 | - |  |  |  |  |  |
| 21 | - |  |  |  |  |  |
| 22 | - |  |  |  |  |  |
| 23 | - |  |  |  |  |  |
| 24 | - |  |  |  |  |  |
| 25 | - |  |  |  |  |  |
| 26 | - |  |  |  |  |  |
| 27 | - |  |  |  |  |  |
| 28 | - |  |  |  |  |  |
| 29 | - |  |  |  |  |  |
| 30 | - |  |  |  |  |  |
| 31 | - |  |  |  |  |  |
| 32 | - |  |  |  |  |  |
| 33 | - |  |  |  |  |  |
| 34 | - |  |  |  |  |  |
| 35 | - |  |  |  |  |  |
| 36 | - |  |  |  |  |  |
| 37 | - |  |  |  |  |  |
| 38 | - |  |  |  |  |  |
| 39 | - |  |  |  |  |  |
| 40 | - |  |  |  |  |  |
| 41 | - |  |  |  |  |  |
| 42 | - |  |  |  |  |  |
| 43 | - |  |  |  |  |  |
| 44 | - |  |  |  |  |  |
| 45 | - |  |  |  |  |  |
| 46 | - |  |  |  |  |  |
| 47 | - |  |  |  |  |  |
| 48 | - |  |  |  |  |  |
| 49 | - |  |  |  |  |  |
| 50 | - |  |  |  |  |  |
| 51 | - |  |  |  |  |  |
| 52 | - |  |  |  |  |  |
| 53 | - |  |  |  |  |  |
| 54 | - |  |  |  |  |  |
| 55 | - |  |  |  |  | 受捐账户可能会是多个账户，用,连接 |
| 56 | - |  |  |  |  |  |
| 57 | - |  |  |  |  | 为了提高队伍列表的检索速度 |
| 58 | - |  |  |  |  |  |
| 59 | - |  |  |  |  |  |
| 60 | - |  |  |  |  |  |
| 61 | - |  |  |  |  |  |
| 62 | - |  |  |  |  |  |
| 63 | - |  |  |  |  |  |
| 64 | - |  |  |  |  | 用于收集暴走队员的地址 |
| 65 | - |  |  |  |  |  |
| 66 | - |  |  |  |  |  |
| 67 | - |  |  |  |  |  |
| 68 | - |  |  |  |  |  |
| 69 | - |  |  |  |  |  |
| 70 | - |  |  |  |  |  |
| 71 | - |  |  |  |  |  |
| 72 | - |  |  |  |  |  |
| 73 | - |  |  |  |  |  |

### t_egg_activity

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 10 | 否 |  | 自动编号 |
| 2 | activityId | varchar | 18 | 否 |  | 活动编号 |
| 3 | year | varchar | 4 | 否 |  | 年度 |
| 4 | activitySubject | varchar | 20 | 否 |  | 活动主题 |
| 5 | activityContent | varchar | 50 | 是 |  | 活动详细内容 |
| 6 | activityTimeStart | datetime |  | 否 |  | 活动启动时间（YYYY/MM/DD hh24:mi:ss） |
| 7 | activityTimeEnd | datetime |  | 否 |  | 活动结束时间（YYYY/MM/DD hh24:mi:ss） |
| 8 | applyTimeStart | datetime |  | 否 |  | 队伍报名启动时间（YYYY/MM/DD hh24:mi:ss） |
| 9 | applyTimeEnd | datetime |  | 否 |  | 队伍报名结束时间（YYYY/MM/DD hh24:mi:ss） |
| 10 | reqistration | decimal | 10,2 | 否 |  | 报名费（参加活动需要的报名费金额） |
| 11 | currentActivityFlag | int | 2 | 否 |  | 当前活动标志（0：非当前活动 1：当前活动） |
| 12 | payStatus | tinyint | 1 | 否 | 0 | 报名费支付启动开关（0：关；1：开） |
| 13 | payTimeStart | datetime |  | 否 |  | 报名费支付启动时间（YYYY/MM/DD hh24:mi:ss） |
| 14 | payPeriod | int | 2 | 否 |  | 允许支付报名费时间（单位：天） |
| 15 | playersMin | int | 1 | 否 |  | 队伍正式队员最小值 |
| 16 | playersMax | int | 1 | 否 |  | 队伍正式队员最大值 |
| 17 | fundraisingAccount | varchar | 32 | 否 |  | 活动筹款账户（临时） |
| 18 | registrationAccount | varchar | 32 | 否 |  | 活动报名费账户（临时） |
| 19 | canPayRegiFeeTeamNum | int | 4 | 否 |  | 允许支付报名费的队伍数 |
| 20 | baozouDate | datetime |  | 是 |  | 暴走日（YYYY/MM/DD hh24:mi:ss） |
| 21 | fundraisingEndDate | datetime |  | 是 |  | 筹款结束日（YYYY/MM/DD hh24:mi:ss） |
| 22 | anonymousAccount | varchar | 32 | 否 |  | 活动匿名捐赠账户（临时） |
| 23 | personalEditEndTime | datetime |  | 是 |  | 个人信息完善截止时间（YYYY/MM/DD hh24:mi:ss） |
| 24 | teamEditEndTime | datetime |  | 是 |  | 队伍信息编辑截止时间（YYYY/MM/DD hh24:mi:ss） |
| 25 | groupEditEndTime | datetime |  | 是 |  | 团体信息编辑截止时间（YYYY/MM/DD hh24:mi:ss） |
| 26 | drawEndDate | datetime |  | 是 |  | 抽签准备截止时间（YYYY/MM/DD hh24:mi:ss） |
| 27 | groupTimeStart | datetime |  | 是 |  | 团体报名启动时间（YYYY/MM/DD hh24:mi:ss） |
| 28 | groupTimeEnd | datetime |  | 是 |  | 团体报名结束时间（YYYY/MM/DD hh24:mi:ss） |
| 29 | registrationEndTime | datetime |  | 是 |  | 报名费缴费截止时间（YYYY/MM/DD hh24:mi:ss） |
| 30 | playersMinAge | int | 2 | 否 | 18 | 允许暴走成员的最小年龄（周岁） |
| 31 | playersMaxAge | int | 2 | 否 | 70 | 允许暴走成员的最大年龄（周岁） |
| 32 | drawDonate | decimal | 10,2 | 否 | 0 | 公众队伍获得活动参与资格的捐赠额（默认10000元） |
| 33 | newsEndTime | datetime |  | 是 |  | 新闻显示截至时间（YYYY/MM/DD hh24:mi:ss） |
| 34 | steamTimeStart | datetime |  | 是 |  | 特筹报名启动时间（YYYY/MM/DD hh24:mi:ss） |
| 35 | steamTimeEnd | datetime |  | 是 |  | 特筹报名结束时间（YYYY/MM/DD hh24:mi:ss） |
| 36 | videoId | varchar | 64 | 是 |  | 官方视频 |
| 37 | targets | varchar | 255 | 是 |  | 筹款标的(,分割) |
| 38 | drawDonateCount | int | 2 | 是 |  | 公众队伍获取抽签资格的最低筹款笔数 |
| 39 | inDraw | int | 32 | 是 |  | 公众已获得正式参与活动的队伍数 |

### t_egg_user

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号 |
| 2 | userId | varchar | 30 | 否 |  | 用户名 |
| 3 | userName | varchar | 20 | 否 |  | 用户真实姓名 |
| 4 | nickName | varchar | 32 | 否 |  | 用户昵称 |
| 5 | birth | datetime |  | 是 |  | 生日 |
| 6 | identityCard | varchar | 18 | 是 |  | 身份证号 |
| 7 | phone | varchar | 11 | 是 |  | 手机 |
| 8 | email | varchar | 48 | 是 |  | 邮箱 |
| 9 | pictureId | varchar | 30 | 是 |  | 头像GUID |
| 10 | donateAccount | varchar | 32 | 是 |  | 用户捐款账户（永久） |
| 11 | uid | varchar | 16 | 否 |  | 用户ID(对外公开用) |
| 12 | nationality | tinyint | 1 | 是 |  | 国籍（0：中国大陆；1：港澳及外籍人士；2：台湾同胞） |
| 13 | passport | varchar | 18 | 是 |  | 护照号 |
| 14 | sex | tinyint | 1 | 是 |  | 性别（0：男；1女） |
| 15 | job | tinyint | 1 | 是 |  | 职业信息（0：学生；1：公司职员；2：国企事业单位职员；3：自由职业者；4：公益从业人员；5：政府机关人员；6：媒体；7：其他） |
| 16 | address | varchar | 50 | 是 |  | 通讯地址（详细地址） |
| 17 | postCode | varchar | 10 | 是 |  | 邮政编码 |
| 18 | urgentName | varchar | 20 | 是 |  | 紧急联系人姓名 |
| 19 | urgentPhone | varchar | 11 | 是 |  | 紧急联系人手机 |
| 20 | contactEmail | varchar | 48 | 是 |  | 联系邮箱 |
| 21 | workUnit | varchar | 255 | 是 |  | 工作单位 |
| 22 | position | varchar | 255 | 是 |  | 工作职位 |
| 23 | nationalityDetail | varchar | 255 | 是 |  | 国籍详细 |
| 24 | hukouProvince | int | 11 | 是 |  | 户籍地址的省或直辖市的地名ID |
| 25 | hukouCity | int | 11 | 是 |  | 户籍地址的市ID |
| 26 | hukouAddress | varchar | 255 | 是 |  | 户籍地址的详细地址 |
| 27 | hukou | varchar | 255 | 是 |  | 户籍地址 |
| 28 | province | int | 11 | 是 |  | 住址的省或直辖市的地名ID |
| 29 | city | int | 11 | 是 |  | 住址的市ID |
| 30 | detailAddress | varchar | 255 | 是 |  | 住址的详细地址 |
| 31 | homeAddress | varchar | 255 | 是 |  | 住址 |
| 32 | contactPhone | varchar | 11 | 是 |  | 联系手机 |
| 33 | insertTime | datetime |  | 是 | CURRENT_TIMESTAMP | 用户首次登录时间 |
| 34 | weixinOpenid | varchar | 255 | 是 |  | 微信公众号的openid |
| 35 | subscribe | int | 4 | 否 | 0 | 0:没有关注 1：已关注 |
| 36 | unionid | varchar | 255 | 是 |  | unionid |

### t_egg_activity_group_team

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 10 | 否 |  | 自动编号 |
| 2 | activityId | varchar | 18 | 否 |  | 活动编号 |
| 3 | teamId | varchar | 18 | 否 |  | 队伍编号 |
| 4 | groupId | varchar | 18 | 是 |  | 团体编号 |
| 5 | teamManifesto | varchar | 50 | 否 |  | 队伍宣言 |
| 6 | raiseTarget | decimal | 10,2 | 否 |  | 筹款目标 |
| 7 | teamTypeId | int | 2 | 否 |  | 队伍类型编号 |
| 8 | paymentStatus | int | 1 | 否 | 0 | 缴费状态（0：未支付；1：可以支付；2：已支付；3：企业未支付；4：企业已支付；5：逾期未缴费自动放弃） |
| 9 | paymentTime | datetime |  | 是 |  | 缴费时间 |
| 10 | openTeamTypeId | int | 2 | 是 |  | 不需要抽签的公众选择的队伍类型编号 |
| 11 | drawOrder | int | 4 | 是 |  | 抽签顺序 |
| 12 | invitationCode | varchar | 16 | 是 |  | 邀请码 |
| 13 | matchAmount | decimal | 10,2 | 是 |  | 配比金额 |
| 14 | fundraisingAccount | varchar | 32 | 是 |  | 队伍筹款账户（临时） |
| 15 | RegistrationAccount | varchar | 32 | 是 |  | 队伍报名费账户（临时） |
| 16 | teamMemberNum | int | 2 | 是 |  | 队员数 |
| 17 | teamStatus | int | 2 | 是 |  | 队伍状态（0：正常；1：特筹未审核；2：队伍解散） |
| 18 | reviewTime | datetime |  | 是 |  | 特筹审核通过时间 |
| 19 | dissolutionTime | datetime |  | 是 |  | 队伍解散时间 |
| 20 | pledgesId | int | 11 | 是 |  | 筹款承诺书图片Id |
| 21 | appendixId | int | 11 | 是 |  | 承诺书附录图片Id |
| 22 | reviewDetail | varchar | 200 | 是 |  | 审核不通过理由 |
| 23 | raiseAmount | decimal | 10,2 | 是 | 0 | 筹款金额 |
| 24 | raiseCount | int | 1 | 是 | 0 | 筹款笔数 |
| 25 | sign | int | 1 | 否 | 0 | 特殊标记(0：默认；1：直通车队伍；2：晚鸟直通车队伍；11：人员变动失去资格；12：缴费到期失去资格；13：早鸟到期失去资格；14：晚鸟到期失去资格) |
| 26 | signTime | datetime |  | 是 |  | 标记时间 |
| 27 | lockingTeamMemberNum | int |  | 是 |  | 个人缴费锁定队员数 |
| 28 | targetReachSendFlag | int |  | 是 | 0 | 目标达成短信是否发送（0：未发送；1：发送） |

### t_egg_temp_team_list

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 10 | 否 |  | 自动编号 |
| 2 | activityId | varchar | 18 | 是 |  | 活动编号 |
| 3 | groupId | varchar | 18 | 是 |  | 团体编号 |
| 4 | teamId | varchar | 18 | 是 |  | 队伍编号 |
| 5 | activityTeamId | int | 10 | 是 |  | 活动队伍编号 |
| 6 | tid | int | 4 | 是 |  | 队伍ID |
| 7 | teamName | varchar | 40 | 是 |  | 队伍名称 |
| 8 | teamTypeId | int | 2 | 是 |  | 队伍类型编号 |
| 9 | teamTypeName | varchar | 10 | 是 |  | 队伍类型名称 |
| 10 | teamFundraisingAccount | varchar | 32 | 是 |  | 队伍筹款账户（临时） |
| 11 | raiseTarget | decimal | 10,2 | 是 |  | 筹款目标 |
| 12 | paymentStatus | tinyint | 1 | 是 |  | 缴费状态（0：未支付；1：可以支付；2：已支付；3：企业未支付；4：企业已支付） |
| 13 | drawOrder | int | 4 | 是 |  | 抽签顺序 |
| 14 | teamMemberNum | int | 2 | 是 |  | 正式队员数 |
| 15 | userId | varchar | 32 | 是 |  | 用户编号 |
| 16 | userStatus | tinyint | 1 | 是 |  | 用户状态（0：待审批；1：已加入；2：已剔出；3：已退出） |
| 17 | userType | tinyint | 1 | 是 |  | 队员类型（0：队长；1：普通队员） |
| 18 | nickName | varchar | 30 | 是 |  | 用户昵称 |
| 19 | phone | varchar | 11 | 是 |  | 手机号码 |
| 20 | captainUserId | varchar | 30 | 是 |  | 用户编号 |
| 21 | captainNickName | varchar | 30 | 是 |  | 队长昵称 |
| 22 | teamStatus | int | 1 | 是 |  | 队伍状态（0：正常；1：特筹未审核；2：队伍解散） |
| 23 | groupStatus | int | 1 | 是 |  | 团体状态（团体队伍时有数据） |
| 24 | raiseAmount | decimal | 15,2 | 是 |  | 筹款金额 |
| 25 | raiseCount | int | 11 | 是 |  | 筹款笔数 |
| 26 | sign | int | 1 | 是 |  | 特殊标记(0：默认；1：直通车队伍；2：晚鸟直通车队伍;) |

### notifications_queue

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 主键ID |
| 2 | guid | varchar | 32 |  |  | 任务ID（生成规则：SMSYYYYMMDD24hmmsssss+3位数字随机数，退订/统计用） |
| 3 | activityId | varchar | 18 |  |  | 活动编号 |
| 4 | type | int | 1 |  |  | 任务类型（1：短信；2：邮件；3：微信） |
| 5 | receiver | varchar | 100 |  |  | 接收目标（手机号、邮箱、微信） |
| 6 | notifications_code | varchar | 20 |  |  | 模板code |
| 7 | send_status | int | 1 |  | 0 | 状态（0：待发送 1：发送成功 2：发送失败 3：发送中 ） |
| 8 | title | varchar | 255 |  |  | 发送标题 |
| 9 | content | longtext | 0 |  |  | 发送内容 |
| 10 | event_result | varchar | 32 |  |  | 发送id |
| 11 | send_id | varchar | 32 |  |  | 返回请求结果 |
| 12 | errmsg | varchar | 1024 |  |  | 错误信息 |
| 13 | userId | varchar | 32 |  |  | 接收者用户名 |
| 14 | name_or_nickname | varchar | 64 |  |  | 接收方姓名 |
| 15 | creater | varchar | 255 |  |  | 创建者 |
| 16 | create_time | datetime | 0 |  |  | 时间 |
| 17 | send_count | int | 1 |  |  | 可重复发送次数 |
| 18 | last_run_time | varchar | 32 |  |  | 最后一次发送时间 |
| 19 | important | bigint | 20 |  |  | 重要度（由低到高，数值越大，代表重要度越高，优先发送，暂定1-5） |
| 20 | can_unsubscribe | int | 2 |  | 0 | 是否可以退订（0：不可退订 1：可以退订）（短信发送时用） |
| 21 | unsubscribe_info | mediumtext | 0 |  |  | 退订内容 |
| 22 | unsubscribe_click_flg | int | 0 |  | 0 | 邮件的判断是否点击过退订按钮（0：否，1：是） |
| 23 | read_flag | int | 0 |  | 0 | 邮件是否阅读（0：未读 1：已读） |
| 24 | read_time | varchar | 32 |  |  | 阅读时间 |

### t_egg_achievem_config

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 10 | 否 |  | 自动编号 |
| 2 | activityId | varchar | 18 | 否 |  | 活动编号 |
| 3 | activityYear | varchar | 4 | 否 |  | 年度 |
| 4 | roadMapPic | mediumblob |  | 否 |  | 暴走地图 |
| 5 | roadMapPicExt | varchar | 64 | 否 |  | 暴走地图扩展名 |
| 6 | roadMapPicName | varchar | 255 | 否 |  | 暴走地图文件名 |
| 7 | achievemCertPic | mediumblob |  | 否 |  | 成绩证书底图 |
| 8 | achievemCertPicExt | varchar | 64 | 否 |  | 成绩证书底图扩展名 |
| 9 | achievemCertPicName | varchar | 255 | 否 |  | 成绩证书底图文件名 |
| 10 | achievemCertShowPic | mediumblob |  | 否 |  | 成绩证书分享图片 |
| 11 | achievemCertShowPicExt | varchar | 64 | 否 |  | 成绩证书分享图片扩展名 |
| 12 | achievemCertShowPicName | varchar | 255 | 否 |  | 成绩证书分享图片文件名 |
| 13 | achievemCertTitle | varchar | 64 | 否 |  | 成绩分享标题 |
| 14 | achievemCertContent | varchar | 200 | 否 |  | 成绩分享内容（简介） |
| 15 | updateTime | datetime |  | 否 |  | 更新时间（YYYY/MM/DD hh24:mi:ss） |
| 16 | mome | text | 100 | 是 |  | 备注 |
| 17 | nickNamePositionH | int | 4 | 否 |  | 昵称显示位置（横） |
| 18 | nickNamePositionV | int | 4 | 否 |  | 昵称显示位置（纵） |
| 19 | nickNameSize | int | 4 | 否 |  | 昵称字体大小 |
| 20 | achievemPositionH | int | 4 | 否 |  | 成绩显示位置（横） |
| 21 | achievemPositionV | int | 4 | 否 |  | 成绩显示位置（纵） |
| 22 | achievemSize | int | 4 | 否 |  | 成绩字体大小 |

### t_egg_group

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 10 | 否 |  | 自动编号 |
| 2 | groupId | varchar | 18 | 否 |  | 团体编号 |
| 3 | groupName | varchar | 30 | 否 |  | 团体名称 |
| 4 | groupManifesto | varchar | 1024 | 否 |  | 团体宣言 |
| 5 | userId | varchar | 32 | 否 |  | 管理员用户编号 |
| 6 | groupType | int | 1 | 否 | 0 | 团体类型（0：普通团体； 1：企业团体；2：公益自筹） |
| 7 | createTime | datetime |  | 否 |  | 组建时间 |
| 8 | remark | varchar | 50 | 是 |  | 备注 |
| 9 | enterpriseId | varchar | 32 | 是 |  | 企业编号/机构编号 |
| 10 | activityId | varchar | 18 | 否 |  | 活动编号 |
| 11 | pictureId | int | 11 | 是 |  | 头像ID |
| 12 | adminPassword | varchar | 40 | 是 |  | 管理员密钥 |
| 13 | password | varchar | 40 | 是 |  | 队长队员密钥 |
| 14 | teamCount | int | 2 | 是 |  | 企业队伍总数 |
| 15 | teamType | varchar | 50 | 是 |  | 可供企业组队选择的队伍类型，用[,]分割 团体类型区分（非组合） |
| 16 | status | tinyint | 1 | 否 | 1 | 团体状态（0：待审批；1:已上线；2：团体解散） |
| 17 | reviewTime | datetime |  | 是 |  | 团体审核时间 |
| 18 | dissolutionTime | datetime |  | 是 |  | 团体解散时间 |

### t_egg_invoice_temp_info

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号,自动增长 |
| 2 | tempid | varchar | 32 | 否 |  | 源编号 |
| 3 | invoiceTitle | varchar | 128 | 是 |  | 发票抬头 |
| 4 | recipientName | varchar | 32 | 是 |  | 收件人姓名 |
| 5 | recipientPhone | varchar | 16 | 是 |  | 收件人联系电话 |
| 6 | recipientCompany | varchar | 128 | 是 |  | 收件人单位 |
| 7 | recipientProvince | varchar | 16 | 是 |  | 收件人地址省 |
| 8 | recipientCity | varchar | 16 | 是 |  | 收件人地址市 |
| 9 | recipientRegions | varchar | 16 | 是 |  | 收件人地址区 |
| 10 | recipientAddress | varchar | 128 | 是 |  | 收件人地址 |
| 11 | recipientZipcode | varchar | 16 | 是 |  | 收件人邮编 |
| 12 | postType | int | 1 | 是 | 0 | 邮寄方式（0：挂号信 1：快递） |
| 13 | orderNo | varchar | 32 | 是 |  | 订单编号 |
| 14 | invoiceType | int | 1 | 是 |  | 发票类型(0:纸质发票；1:电子发票-个人；2：电子发票-单位；3：电子发票-匿名） |
| 15 | creditCode | varchar | 32 | 是 |  | 证件号 |
| 16 | email | varchar | 64 | 是 |  | 邮箱 |
| 17 | creditType | int | 1 | 是 |  | 证件类型(0：默认无意义；1:身份证；2：护照；3：机构证件） |

### t_egg_activity_teamtype

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 10 | 否 |  | 自动编号 |
| 2 | activityId | varchar | 18 | 否 |  | 活动编号 |
| 3 | teamTypeId | int | 2 | 否 |  | 队伍类型编号 |
| 4 | selectControl | tinyint | 1 | 否 | 0 | 队伍类型是否允许公众选择（0：允许；1：不允许） |
| 5 | drawControl | tinyint | 1 | 否 | 0 | 是否需要抽签（0：需要抽签；1：不需要抽签） |
| 6 | startTime | datetime |  | 是 |  | 报名启动时间（YYYY/MM/DD hh24:mi:ss） |
| 7 | endTime | datetime |  | 是 |  | 报名结束时间（YYYY/MM/DD hh24:mi:ss） |
| 8 | editEndTime | datetime |  | 是 |  | 队伍信息编辑截止时间（YYYY/MM/DD hh24:mi:ss） |
| 9 | registrationEndTime | datetime |  | 是 |  | 报名费缴费截止时间（YYYY/MM/DD hh24:mi:ss） |
| 10 | raiseTarget | decimal | 10,2 | 否 |  | 筹款目标 |
| 11 | teamTypeName | varchar | 10 | 是 |  | 队伍类型名称，冗余字段 |
| 12 | paymentRule | int | 10,2 | 否 | 0 | 报名费缴费规则（0：按队伍缴费；1：按个人缴费） |
| 13 | teamCost | decimal |  | 是 |  | 队伍报名费 |
| 14 | personalCost | decimal | 10,2 | 是 |  | 个人报名费 |
| 15 | paymentTime | datetime | 10,2 | 是 |  | 缴费开始时间（YYYY/MM/DD hh24:mi:ss） |
| 16 | paymentMemberOpt | int |  | 否 | 0 | 个人缴费 -- 缴费完成后是否支持操作【增加/删除】队员（0：支持；1：不支持） |


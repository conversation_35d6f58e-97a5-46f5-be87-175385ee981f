# 证书管理模块 数据字典

## 模块概述

**业务模块**: 证书管理  
**关键词**: certificate, 证书  
**相关表数量**: 19  
**涉及系统**: 5个  

## 相关表清单

| 序号 | 表名 | 所属系统 | 字段数 | 说明 |
|------|------|----------|--------|------|
| 1 | 证书模板表 | HORN系统 | 20 |  |
| 2 | t_egg_certificate_file | EGG系统 | 15 |  |
| 3 | t_xxbz_certificate_file | 小小包子系统 | 15 |  |
| 4 | 证书模板文字属性表 | 业务管理系统 | 14 |  |
| 5 | 机构证书到期邮件记录表 | HORN系统 | 14 |  |
| 6 | t_egg_certificate_property | EGG系统 | 13 |  |
| 7 | t_horn_certificate_property | 小小包子系统 | 13 |  |
| 8 | 成绩证书表 | 财务支付系统 | 12 |  |
| 9 | t_egg_certificate_template | EGG系统 | 11 |  |
| 10 | t_xxbz_certificate_template | 小小包子系统 | 11 |  |
| 11 | 证书模板审核表 | HORN系统 | 10 |  |
| 12 | 证书模板表 | 业务管理系统 | 9 |  |
| 13 | 证书表 | HORN系统 | 8 |  |
| 14 | 成绩证书模板表 | 财务支付系统 | 7 |  |
| 15 | 证书图片表 | 业务管理系统 | 7 |  |
| 16 | 月捐证书关联表 | 财务支付系统 | 4 |  |
| 17 | 年度证书关联表 | 财务支付系统 | 4 |  |
| 18 | 订单和证书关联表 | 财务支付系统 | 3 |  |
| 19 | 证书模板表2 | 业务管理系统 | 0 |  |

## 主要表结构详情

### 证书模板表 (HORN系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | version | int | 2 | 是 |  | 版本号 |
| 3 | accountHolderId | varchar | 32 | 是 |  | 模板所属的实体id，系统模板为system |
| 4 | belongsName | varchar | 32 | 是 |  | 模板所属的名称 |
| 5 | templatePicId | int | 11 | 是 |  | 模板图片的id |
| 6 | templateDeadline | datetime |  | 是 |  | 模板使用截止日 |
| 7 | type | int | 1 | 是 | 0 | 模板类型（0：捐赠证书（通用版）；1：活动用捐赠证书；2：项目用捐赠证书；3：月捐用捐赠证书；4：年度捐赠证书；5：参与证书；6：完赛证书；7：月捐证书 8：日捐证书 9日捐用捐赠证书） |
| 8 | lineHeight | int | 2 | 是 | 45 | 行间距 |
| 9 | margin | int | 3 | 是 | 220 | 两边间隔(一般是尊敬的{1}fontX的2倍） |
| 10 | stampPicId | int | 11 | 是 |  | 印章图片的id（不使用） |
| 11 | status | int | 1 | 是 | 0 | 状态（0：草稿；1：启用；2：停用；3：删除；4：停用中编辑，共通和年度使用； 5：老版本模板，共通和年度使用；6：审核不通过；7：撤销证书；8：审核中） |
| 12 | creater | varchar | 255 | 是 |  | 创建者 |
| 13 | createTime | datetime | 0 | 是 |  | 创建时间 |
| 14 | updater | varchar | 255 | 是 |  | 更新者 |
| 15 | updateTime | datetime | 0 | 是 |  | 更新时间 |
| 16 | institutionId | int | 11 | 是 |  | 公募机构id（系统模板为空） |
| 17 | oldFlag | int | 1 | 是 | 0 | 是否为旧数据（1：是；0：否），旧数据修改为新数据后删除此字段 |
| 18 | insId | int | 11 | 是 |  | 机构id |
| 19 | ifImport | int | 1 | 是 | 0 | 是否需要导入（0：不需要；1：需要） |
| 20 | createflag | int | 1 | 是 | 0 | 参与证书不需要导入时使用。定时器是否自动生成参与成员（0：否；1：是） |

### t_egg_certificate_file (EGG系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号 |
| 2 | fileData | mediumblob |  | 否 |  | 图片字节流 |
| 3 | fileName | varchar | 255 | 否 |  | 原始图名称 |
| 4 | fileType | varchar | 32 | 否 |  | 原始图类型  jpg/png/bmp/jpge/gif |
| 5 | uploadTime | datetime |  | 否 |  | 图片上传时间（YYYY/MM/DD hh24:mi:ss） |
| 6 | fileWidth | int | 11 | 是 |  | 图片原始宽度 |
| 7 | fileHeight | int | 11 | 是 |  | 图片原始高度 |
| 8 | machineReview | int | 2 | 是 | 0 | 图片机械审核结果（0未审核，1过审，2未过审） |
| 9 | machineReviewTime | datetime |  | 是 |  | 图片机械审核时间 |
| 10 | artifactReview | int | 2 | 是 | 0 | 图片人工审核结果（0未审核，1过审，2未过审） |
| 11 | artifactReviewTime | datetime |  | 是 |  | 图片人工审核时间 |
| 12 | artifactReviewer | varchar | 255 | 是 |  | 图片审核者 |
| 13 | artifactStatus | int | 2 | 是 | 0 | 是否人工审核过（0否，1是） |
| 14 | reviewMark | int | 2 | 是 | 0 | 是否需要审核标记（0不需要，1需要） |
| 15 | holderType | int | 11 | 是 |  | 图片使用对象类型（1活动2项目3月捐...） |

### t_xxbz_certificate_file (小小包子系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | int | 11 | 否 |  | 自动编号 |
| 2 | fileData | mediumblob |  | 否 |  | 图片字节流 |
| 3 | fileName | varchar | 255 | 否 |  | 原始图名称 |
| 4 | fileType | varchar | 32 | 否 |  | 原始图类型  jpg/png/bmp/jpge/gif |
| 5 | uploadTime | datetime |  | 否 |  | 图片上传时间（YYYY/MM/DD hh24:mi:ss） |
| 6 | fileWidth | int | 11 | 是 |  | 图片原始宽度 |
| 7 | fileHeight | int | 11 | 是 |  | 图片原始高度 |
| 8 | machineReview | int | 2 | 是 | 0 | 图片机械审核结果（0未审核，1过审，2未过审） |
| 9 | machineReviewTime | datetime |  | 是 |  | 图片机械审核时间 |
| 10 | artifactReview | int | 2 | 是 | 0 | 图片人工审核结果（0未审核，1过审，2未过审） |
| 11 | artifactReviewTime | datetime |  | 是 |  | 图片人工审核时间 |
| 12 | artifactReviewer | varchar | 255 | 是 |  | 图片审核者 |
| 13 | artifactStatus | int | 2 | 是 | 0 | 是否人工审核过（0否，1是） |
| 14 | reviewMark | int | 2 | 是 | 0 | 是否需要审核标记（0不需要，1需要） |
| 15 | holderType | int | 11 | 是 |  | 图片使用对象类型（1活动2项目3月捐...） |

### 证书模板文字属性表 (业务管理系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 20 | 否 |  | ID，1开始的自然数，自动增长 |
| 2 | templetId | int | 11 | 否 |  | 成绩证书模板Id |
| 3 | templateCharacter | varchar | 255 | 否 |  | 文字内容 |
| 4 | fontX | int | 11 | 否 |  | X位置 |
| 5 | fontY | int | 11 | 否 |  | Y位置 |
| 6 | fontColorR | int | 3 |  |  | 颜色R |
| 7 | fontColorG | int | 3 |  |  | 颜色G |
| 8 | fontColorB | int | 3 |  |  | 颜色B |
| 9 | fontStyle | varchar | 32 | 否 |  | 字体样式 |
| 10 | fontType | varchar | 255 |  |  | 字体类型 |
| 11 | fontSize | int | 11 | 否 |  | 字体大小 |
| 12 | note | varchar | 255 |  |  | 备注 |
| 13 | status | int | 1 | 否 |  | 是否启用（0：启用；1：不启用） |
| 14 | type | int | 2 | 否 |  | 内容类型（0：文字；1：称呼；2：署名；3：日期；4：印章；5：订单编号） |

### 机构证书到期邮件记录表 (HORN系统)

| 序号 | 字段名 | 数据类型 | 长度 | 允许空 | 默认值 | 说明 |
|------|--------|----------|------|--------|--------|------|
| 1 | id | bigint | 0 | 否 |  | 自增id |
| 2 | content | varchar | 1024 |  |  | 内容 |
| 3 | institutionEntityGuid | varchar | 64 |  |  | 机构guid |
| 4 | type | int | 0 |  |  | 类型（0：三合一到期  1：资质证到期  2：三合一和资助证书都到期） |
| 5 | msg | varchar | 255 |  |  | 错误提示 |
| 6 | endTime | datetime | 0 |  |  | 到期时间 |
| 7 | startTime | datetime | 0 |  |  | 开始时间 |
| 8 | receiver | varchar | 255 |  |  | 收件人 |
| 9 | picId | varchar | 255 |  |  | 到期证书图片（，隔开） |
| 10 | sendType | int | 0 |  |  | 通知类型（0：站内信；1：邮件） |
| 11 | sendGuid | varchar | 255 |  |  | 邮件发送的mailid |
| 12 | sendTime | datetime | 0 |  |  | 发送时间 |
| 13 | status | int | 0 |  |  | 状态（0：创建成功；1：创建失败；） |
| 14 | remindId | varchar | 0 |  |  | 提醒ID(rem开头） |

